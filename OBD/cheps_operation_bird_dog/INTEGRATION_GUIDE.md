# OBD Reporter Integration Guide

Simple, modular reporting system that can be easily added to your existing OBD simulation workflows.

## Quick Integration

### Option 1: One-Line Integration
```python
from obd_reporter import quick_report

# After your OBD simulation runs:
report_path = quick_report(slots_df, appointments_df)
```

### Option 2: Custom Integration
```python
from obd_reporter import OBDReporter

# Create reporter
reporter = OBDReporter("my_reports")

# Analyze your data
analysis = reporter.analyze_utilization(slots_df, appointments_df)

# Generate custom report
report_path, json_path = reporter.save_report(analysis, "My Custom OBD Report")
```

## What You Get

### Timestamped Reports
- **Text Report**: `OBD_Report_20251001_013358.txt` - Human-readable analysis
- **JSON Data**: `OBD_Data_20251001_013358.json` - Machine-readable data for further analysis

### Report Contents
```
📊 SUMMARY METRICS:
   Total Slots: 88
   Open Slots: 27 (30.7%)
   Average Utilization: 69.3%
   Potential Patient Matches: 20
   Average Patient Wait: 36.8 days

👨‍⚕️ PROVIDER UTILIZATION:
   Dr. Smith: 55.2% (13 open of 29 slots) ⚠️
   NP Jones: 80.0% (6 open of 30 slots) ✅
   Dr. Brown: 72.4% (8 open of 29 slots) ✅

🔍 WHY SLOTS AREN'T UTILIZED:
   Weekend Slots: 5 slots
   Timing Issues: 20 slots
   Other Factors: 2 slots

💡 KEY INSIGHTS:
   • 27 open slots available for 20 waiting patients
   • Main opportunity: Provider balancing
   • Potential wait time reduction: 37 days

🎯 RECOMMENDATIONS:
   • Focus on underutilized providers (marked ⚠️)
   • Review weekend slot scheduling policies
   • Prioritize patient-slot matching to reduce wait times
```

## Required Data Columns

### Minimum Required (slots_df):
- `AVAILABLE_OPENINGS` - Number of open slots
- `PROV_NAME` - Provider name

### Enhanced Analysis (optional columns):
- `NUM_APTS_SCHEDULED` - Scheduled appointments
- `ORG_REG_OPENINGS` - Total slot capacity
- `SLOT_BEGIN_TIME` - Slot datetime (for weekend analysis)
- `DEPARTMENT_ID` - Department identifier

### Appointment Data (appointments_df, optional):
- `AppointmentDateTime` - Appointment date/time
- `DepartmentEpicId` - Department ID
- `ProviderType` - Provider type

## Modular Design

### Easy Customization
```python
# Custom output directory
reporter = OBDReporter("custom_reports")

# Custom report title
reporter.save_report(analysis, "Weekly OBD Analysis")

# Skip JSON export
reporter.save_report(analysis, save_json=False)
```

### Extend Analysis
```python
class CustomOBDReporter(OBDReporter):
    def analyze_utilization(self, slots_df, appointments_df=None):
        # Call parent method
        results = super().analyze_utilization(slots_df, appointments_df)
        
        # Add your custom analysis
        results['custom_metric'] = your_calculation()
        
        return results
```

## Integration Examples

### In Your Main OBD Script
```python
def run_obd_simulation():
    # Your existing OBD logic
    slots_df = load_slots_data()
    appointments_df = load_appointments_data()
    
    # Run OBD processing
    process_obd_matching(slots_df, appointments_df)
    
    # Generate report (just add this line!)
    from obd_reporter import quick_report
    quick_report(slots_df, appointments_df, "daily_reports", "Daily OBD Report")
```

### Scheduled Reporting
```python
import schedule
from obd_reporter import quick_report

def daily_obd_report():
    slots_df = get_current_slots()
    appointments_df = get_current_appointments()
    quick_report(slots_df, appointments_df, "daily_reports")

# Run daily at 6 AM
schedule.every().day.at("06:00").do(daily_obd_report)
```

## Benefits

✅ **Minimal Code**: Just 2 lines to add reporting  
✅ **Timestamped**: Automatic date/time stamping  
✅ **Modular**: Easy to customize and extend  
✅ **Actionable**: Clear insights and recommendations  
✅ **Multiple Formats**: Text reports + JSON data  
✅ **No Dependencies**: Uses standard libraries  

## File Structure
```
obd_reports/
├── OBD_Report_20251001_013358.txt    # Human-readable report
├── OBD_Data_20251001_013358.json     # Machine-readable data
├── OBD_Report_20251001_140215.txt    # Next run
└── OBD_Data_20251001_140215.json
```

This system automatically captures utilization insights every time your OBD simulation runs, with minimal code changes to your existing workflow!
