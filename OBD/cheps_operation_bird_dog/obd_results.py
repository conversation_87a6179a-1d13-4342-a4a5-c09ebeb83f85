#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Wed Jul  2 16:36:05 2025

@author: armangetzen

This script contains functions to run analytics on open slots, rescheduable
patients, and movement opportunities to support data reports for Operation
Bird Dog.

"""

import pandas as pd
import os
import json
import random
from ast import literal_eval
import math
from datetime import date, time, datetime, timedelta
import numpy as np

class OBDResults:
    
    # Initialization of Class Variables
    def __init__(self):
        return
    
    def initialize_data_sources(self, data_dir, dept_string):
        # Same as the results directory that the OBDScheduler outputs are saved to
        self.data_dir = data_dir
        # Load in reschedule opportunities, list of eligible patients, and all slots overall in the dept
        self.slots_opportunities = pd.read_csv(f"{data_dir}/{dept_string}_slot_reschedule_opportunities.csv")
        self.patient_list = pd.read_csv(f"{data_dir}/{dept_string}_mrn_list.csv")
        # Slots data only includes slots within the slot horizon, metrics will explicitly mention this
        self.slots_data = pd.read_csv(f"{data_dir}/{dept_string}_all_slots_data.csv")

        return


    def calculate_OBD_metrics(self, slot_horizon):

        self.slots_data["Utilization"] = self.slots_data["NUM_APTS_SCHEDULED"]/self.slots_data["ORG_REG_OPENINGS"]

        # Calculate basic metrics
        metrics = {}

        # Total open slots found
        metrics["total_open_slots"] = len(self.slots_opportunities)

        # Total eligible patients
        metrics["total_eligible_patients"] = self.slots_opportunities["NUM_ELIGIBLE_PATIENTS"].sum()

        # Average wait time for eligible patients
        metrics["avg_patient_wait_days"] = self.slots_opportunities["AVG_PATIENT_WAIT_DAYS"].mean()

        # Utilization statistics
        metrics["avg_provider_utilization"] = self.slots_data["Utilization"].mean()
        metrics["providers_over_95_percent"] = len(self.slots_data[self.slots_data["Utilization"] >= 0.95])

        # Slot type breakdown
        slot_type_stats = self.slots_opportunities["SLOT_BLOCKS"].value_counts().to_dict()
        metrics["slot_types"] = slot_type_stats

        # Provider type breakdown
        provider_type_stats = self.slots_opportunities["PROV_TYPE"].value_counts().to_dict()
        metrics["provider_types"] = provider_type_stats

        # Capacity constraints
        metrics["slots_with_limited_capacity"] = len(self.slots_opportunities[self.slots_opportunities["PROVIDER_AVAILABLE_CAPACITY"] < 5])
        metrics["total_available_capacity"] = self.slots_opportunities["PROVIDER_AVAILABLE_CAPACITY"].sum()

        return metrics

    def analyze_patient_eligibility_issues(self, appt_data, slot_data, min_day_apart):
        """
        Analyze why patients scheduled in the future cannot be moved into open slots
        """
        eligibility_stats = {
            "total_future_appointments": 0,
            "ineligible_reasons": {
                "insufficient_time_gap": 0,
                "appointment_too_long": 0,
                "provider_type_mismatch": 0,
                "slot_type_mismatch": 0,
                "provider_overbooked": 0,
                "not_np_appointment": 0,
                "referral_appointment": 0,
                "opinion_appointment": 0
            }
        }

        # Get all future appointments
        today = datetime.now()
        future_appts = appt_data[pd.to_datetime(appt_data["AppointmentInstant"]) > today]
        eligibility_stats["total_future_appointments"] = len(future_appts)

        # Get all available slots
        available_slots = slot_data[
            (slot_data["NUM_APTS_SCHEDULED"] == 0) &
            (slot_data["ORG_REG_OPENINGS"] > 0)
        ]

        # Analyze each future appointment against available slots
        for _, appt in future_appts.iterrows():
            appt_eligible_for_any_slot = False

            for _, slot in available_slots.iterrows():
                slot_begin_time = pd.to_datetime(slot["SLOT_BEGIN_TIME"])
                appt_time = pd.to_datetime(appt["AppointmentInstant"])

                # Check time gap requirement
                if appt_time <= (slot_begin_time + timedelta(days=min_day_apart)):
                    continue

                # Check appointment length vs slot length
                slot_length = (pd.to_datetime(slot["SLOT_END_TIME"]) - slot_begin_time).total_seconds() / 60
                if appt["AppointmentLengthInMinutes"] > slot_length:
                    continue

                # Check if it's an NP appointment
                if not ("np" in str(appt.get("VisitType", "")).lower()):
                    continue

                # Check for opinion appointments
                if "opinion" in str(appt.get("VisitType", "")).lower():
                    continue

                # Check for referral appointments
                referral_keywords = ['ref', 'referral', 'refer', 'consultation', 'consult']
                is_referral = any(keyword in str(appt.get("VisitType", "")).lower() for keyword in referral_keywords)
                if is_referral:
                    continue

                # Check provider type match
                if appt.get("PrimaryVisitProviderType") != slot.get("PROV_TYPE"):
                    continue

                # If we get here, the appointment is eligible for this slot
                appt_eligible_for_any_slot = True
                break

            # If not eligible for any slot, categorize the reason
            if not appt_eligible_for_any_slot:
                # Find the most common reason for ineligibility
                reasons_found = []

                for _, slot in available_slots.iterrows():
                    slot_begin_time = pd.to_datetime(slot["SLOT_BEGIN_TIME"])
                    appt_time = pd.to_datetime(appt["AppointmentInstant"])

                    if appt_time <= (slot_begin_time + timedelta(days=min_day_apart)):
                        reasons_found.append("insufficient_time_gap")
                        continue

                    slot_length = (pd.to_datetime(slot["SLOT_END_TIME"]) - slot_begin_time).total_seconds() / 60
                    if appt["AppointmentLengthInMinutes"] > slot_length:
                        reasons_found.append("appointment_too_long")
                        continue

                    if not ("np" in str(appt.get("VisitType", "")).lower()):
                        reasons_found.append("not_np_appointment")
                        continue

                    if "opinion" in str(appt.get("VisitType", "")).lower():
                        reasons_found.append("opinion_appointment")
                        continue

                    referral_keywords = ['ref', 'referral', 'refer', 'consultation', 'consult']
                    is_referral = any(keyword in str(appt.get("VisitType", "")).lower() for keyword in referral_keywords)
                    if is_referral:
                        reasons_found.append("referral_appointment")
                        continue

                    if appt.get("PrimaryVisitProviderType") != slot.get("PROV_TYPE"):
                        reasons_found.append("provider_type_mismatch")
                        continue

                # Count the most common reason
                if reasons_found:
                    most_common_reason = max(set(reasons_found), key=reasons_found.count)
                    eligibility_stats["ineligible_reasons"][most_common_reason] += 1

        return eligibility_stats

    def store_OBD_metrics(self, metrics, eligibility_stats, dept_string):
        """
        Generate and store comprehensive reports
        """
        # Create summary report
        report = {
            "department": dept_string,
            "generated_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "obd_metrics": metrics,
            "eligibility_analysis": eligibility_stats
        }

        # Save detailed report as JSON
        report_file = f"{self.data_dir}/{dept_string}_obd_analysis_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        # Create human-readable summary
        summary_lines = [
            f"Operation Bird Dog Analysis Report - {dept_string}",
            f"Generated: {report['generated_date']}",
            "",
            "=== OPEN SLOT OPPORTUNITIES ===",
            f"Total open slots found: {metrics['total_open_slots']}",
            f"Total eligible patients: {metrics['total_eligible_patients']}",
            f"Average patient wait time: {metrics['avg_patient_wait_days']:.1f} days",
            f"Total available provider capacity: {metrics['total_available_capacity']} appointments",
            "",
            "=== PROVIDER UTILIZATION ===",
            f"Average provider utilization: {metrics['avg_provider_utilization']:.1%}",
            f"Providers over 95% utilization: {metrics['providers_over_95_percent']}",
            f"Slots with limited capacity (<5): {metrics['slots_with_limited_capacity']}",
            "",
            "=== SLOT TYPE BREAKDOWN ===",
        ]

        for slot_type, count in metrics['slot_types'].items():
            summary_lines.append(f"  {slot_type}: {count} slots")

        summary_lines.extend([
            "",
            "=== PROVIDER TYPE BREAKDOWN ===",
        ])

        for prov_type, count in metrics['provider_types'].items():
            summary_lines.append(f"  {prov_type}: {count} slots")

        summary_lines.extend([
            "",
            "=== PATIENT ELIGIBILITY ISSUES ===",
            f"Total future appointments analyzed: {eligibility_stats['total_future_appointments']}",
            "",
            "Reasons patients cannot be moved to open slots:",
        ])

        for reason, count in eligibility_stats['ineligible_reasons'].items():
            if count > 0:
                reason_display = reason.replace("_", " ").title()
                percentage = (count / eligibility_stats['total_future_appointments']) * 100 if eligibility_stats['total_future_appointments'] > 0 else 0
                summary_lines.append(f"  {reason_display}: {count} patients ({percentage:.1f}%)")

        # Save summary report
        summary_file = f"{self.data_dir}/{dept_string}_obd_summary_report.txt"
        with open(summary_file, 'w') as f:
            f.write('\n'.join(summary_lines))

        return report_file, summary_file
        