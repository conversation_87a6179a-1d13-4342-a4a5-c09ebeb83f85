# OBD Test Framework

A comprehensive testing framework for the Operation Bird Dog (OBD) system that provides test data generation, expected output calculation, result validation, and interactive visualization.

## 🚀 Quick Start - Choose Your Testing Approach

### Option 1: Simple Test (RECOMMENDED for beginners)
**Best for**: Quick testing, understanding the system, generating charts
```bash
python simple_obd_test.py
```
**What it does**:
- ✅ Generates realistic test data
- ✅ Creates beautiful charts and visualizations
- ✅ Provides simple metrics analysis
- ✅ No complex validation - just shows what the data looks like

### Option 2: Demo Test
**Best for**: Testing the full framework with one scenario
```bash
python run_obd_tests.py --mode demo
```
**What it does**:
- ✅ Runs one complete test scenario
- ✅ Generates expected vs actual comparisons
- ✅ Creates interactive dashboard
- ✅ Shows pass/fail validation

### Option 3: Full Test Suite
**Best for**: Comprehensive testing with multiple scenarios
```bash
python run_obd_tests.py --mode full
```
**What it does**:
- ✅ Runs 4 different test scenarios
- ✅ Comprehensive validation and reporting
- ✅ Multiple charts and analysis
- ✅ Complete dashboard with all metrics

### Option 4: Generate Sample Data Only
**Best for**: Creating test data to use with the real OBD system
```bash
python run_obd_tests.py --mode data-only --output sample_data
```

## 📊 What You Get - Lots of Charts and Metrics!

### Charts Generated:
1. **📈 Slot Availability Overview** - Pie charts showing open vs booked slots
2. **👨‍⚕️ Provider Utilization** - Bar charts with color-coded utilization rates
3. **🏥 Department Analysis** - Appointments and slots by department
4. **⏰ Wait Time Distribution** - Histogram of patient wait times
5. **📊 Key Metrics Summary** - Bar chart of important numbers
6. **🎯 Test Results** - Pass/fail visualization
7. **💼 Capacity Analysis** - Provider capacity breakdown
8. **📋 Slot Types** - Distribution of different appointment types

### Metrics Calculated:
- Total slots and open slot percentage
- Provider utilization rates (with thresholds)
- Patient wait times (average, max, distribution)
- Department-level statistics
- Appointment type breakdowns
- Capacity analysis

## Framework Components

### 1. Test Data Generator (`obd_test_data_generator.py`)

Generates realistic test data including:
- **Slot Data**: Provider schedules, slot types, utilization rates
- **Appointment Data**: Patient appointments, visit types, referrals
- **Test Scenarios**: Predefined scenarios for different testing needs

**Key Features:**
- Configurable department types (Neurology, Orthopedics, PM&R)
- Realistic provider schedules and utilization patterns
- Various appointment types including referrals and urgents
- Edge cases for comprehensive testing

**Usage:**
```python
from obd_test_data_generator import OBDTestDataGenerator

generator = OBDTestDataGenerator()
scenarios = generator.create_test_scenarios()

# Generate specific data
slots_df = generator.generate_slot_data(num_days=30)
appointments_df = generator.generate_appointment_data(num_patients=100)
```

### 2. Expected Output Calculator (`obd_expected_outputs.py`)

Calculates expected outputs for validation:
- **Slot Opportunities**: Expected reschedule opportunities
- **Patient Lists**: Expected eligible patient lists
- **Metrics**: Expected OBD performance metrics
- **Eligibility Statistics**: Expected patient eligibility analysis

**Usage:**
```python
from obd_expected_outputs import OBDExpectedOutputs

calculator = OBDExpectedOutputs()
expected_opportunities = calculator.calculate_expected_slot_opportunities(
    slots_df, appointments_df, config
)
```

### 3. Test Validator (`obd_test_validator.py`)

Validates actual outputs against expected results:
- **File Comparison**: CSV and JSON file validation
- **Metrics Comparison**: Numerical accuracy with tolerance
- **Business Rules**: Validates OBD business logic compliance
- **Data Integrity**: Cross-file consistency checks

**Usage:**
```python
from obd_test_validator import OBDTestValidator

validator = OBDTestValidator(tolerance_percent=5.0)
results = validator.validate_test_scenario(
    scenario_name, actual_files, expected_files, config
)
```

### 4. Interactive Dashboard (`obd_test_dashboard.py`)

Creates visual dashboards with:
- **Metrics Comparison Charts**: Expected vs actual metrics
- **Slot Analysis**: Open slots and patient distribution
- **Provider Utilization**: Capacity and utilization analysis
- **Wait Time Distribution**: Patient wait time statistics

**Features:**
- HTML dashboard with interactive charts
- PNG chart exports for reports
- Comprehensive test summaries
- Pass/fail status visualization

### 5. Test Runner (`obd_test_runner.py`)

Main orchestrator that:
- Executes complete test suites
- Manages test data and outputs
- Coordinates validation and reporting
- Generates comprehensive results

## Test Scenarios

The framework includes several predefined test scenarios:

### 1. Basic Matching
- **Purpose**: Test fundamental slot-patient matching
- **Data**: Balanced slots and appointments with clear matches
- **Expected**: High success rate with predictable metrics

### 2. High Utilization
- **Purpose**: Test system behavior with limited open slots
- **Data**: 90%+ provider utilization, few open slots
- **Expected**: Lower opportunity counts, higher competition

### 3. Referral Heavy
- **Purpose**: Test referral filtering functionality
- **Data**: 40% referral appointments that should be filtered
- **Expected**: Reduced eligible patient counts

### 4. Edge Cases
- **Purpose**: Test boundary conditions and error handling
- **Data**: Mismatched lengths, provider types, time constraints
- **Expected**: Appropriate filtering and validation

## Output Files

### Test Data Files
- `{date}_OBD_Slot_Data_{scenario}.csv`: Generated slot data
- `{date}_OBD_Appt_Data_{scenario}.csv`: Generated appointment data
- `{scenario}_test_input.txt`: Test configuration file

### Expected Output Files
- `expected_{scenario}_slot_reschedule_opportunities.csv`: Expected slot opportunities
- `expected_{scenario}_mrn_list.csv`: Expected patient list
- `expected_{scenario}_metrics.json`: Expected metrics and statistics

### Actual Output Files
- `{scenario}_slot_reschedule_opportunities.csv`: Actual OBD slot opportunities
- `{scenario}_mrn_list.csv`: Actual OBD patient list
- `{scenario}_obd_analysis_report.json`: Actual OBD metrics

### Validation and Reports
- `{scenario}_validation_report.txt`: Detailed validation results
- `obd_test_dashboard.html`: Interactive dashboard
- `test_summary_report.txt`: Overall test summary
- `complete_test_results.json`: Machine-readable results

### Visualization Files
- `metrics_comparison.png`: Metrics comparison chart
- `slot_opportunities.png`: Slot analysis chart
- `eligibility_analysis.png`: Patient eligibility breakdown
- `provider_utilization.png`: Provider capacity analysis
- `wait_time_distribution.png`: Wait time statistics

## Configuration

### Test Parameters
```python
test_config = {
    'min_day_apart': 7,           # Minimum days between appointments
    'max_slot_horizon': 42,       # Maximum days ahead to look
    'provider_types': 'Physician,Nurse Practitioner',
    'allow_urgents': 'Y',         # Allow urgent slot scheduling
    'tolerance_percent': 5.0      # Validation tolerance
}
```

### Department Configuration
```python
department_config = {
    'dept_id': 999001,
    'name': 'Test Neurology',
    'provider_types': ['Physician', 'Nurse Practitioner'],
    'slot_types': ['Neuromuscular', 'MS', 'Epilepsy'],
    'referral_types': ['Referral Consult', 'New Referral']
}
```

## Validation Metrics

### Key Metrics Validated
- **Total Open Slots**: Number of available scheduling opportunities
- **Total Eligible Patients**: Patients who can be rescheduled
- **Average Wait Days**: Mean patient wait time reduction
- **Provider Utilization**: Capacity usage statistics
- **Eligibility Breakdown**: Reasons for patient ineligibility

### Validation Criteria
- **Numeric Accuracy**: Within 5% tolerance by default
- **Data Integrity**: Cross-file consistency
- **Business Rules**: OBD logic compliance
- **File Structure**: Expected columns and formats

## Advanced Usage

### Custom Test Scenarios
```python
# Create custom test data
generator = OBDTestDataGenerator()
custom_slots = generator.generate_slot_data(
    num_days=45, 
    slots_per_day_range=(10, 20)
)

# Run specific validation
validator = OBDTestValidator(tolerance_percent=3.0)
results = validator.validate_test_scenario(
    'custom_test', actual_files, expected_files, config
)
```

### Integration with CI/CD
```bash
# Run tests in CI pipeline
python run_obd_tests.py --mode full --output ci_results
exit_code=$?

# Check results
if [ $exit_code -eq 0 ]; then
    echo "All tests passed"
else
    echo "Tests failed"
    exit 1
fi
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed
2. **File Path Issues**: Check that input files exist and are readable
3. **Memory Issues**: Reduce test data size for large scenarios
4. **Validation Failures**: Check tolerance settings and expected outputs

### Debug Mode
```python
# Enable detailed logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Run with error handling
try:
    test_runner = OBDTestRunner()
    results = test_runner.run_all_tests()
except Exception as e:
    print(f"Debug info: {str(e)}")
```

## Dependencies

- pandas: Data manipulation and analysis
- numpy: Numerical computations
- matplotlib: Chart generation
- seaborn: Statistical visualization
- json: JSON file handling
- datetime: Date/time operations

## Future Enhancements

1. **Real OBD Integration**: Connect to actual OBD system instead of simulation
2. **Performance Testing**: Add load testing and performance metrics
3. **Historical Analysis**: Compare test results over time
4. **Automated Regression**: Detect performance regressions
5. **Custom Metrics**: User-defined validation criteria

## Support

For questions or issues with the test framework:
1. Check the validation reports for detailed error information
2. Review the dashboard for visual analysis of failures
3. Examine the generated test data for data quality issues
4. Verify configuration parameters match expected values

The framework provides comprehensive logging and error reporting to help diagnose issues quickly.
