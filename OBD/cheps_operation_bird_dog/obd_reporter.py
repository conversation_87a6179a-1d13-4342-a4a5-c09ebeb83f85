#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBD Reporter - Modular reporting for OBD simulation runs

Simple, modular system to generate utilization reports from OBD data.
Can be easily integrated into existing OBD workflows.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import json

class OBDReporter:
    """Modular OBD reporting system"""
    
    def __init__(self, output_dir="obd_reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def analyze_utilization(self, slots_df, appointments_df=None):
        """
        Analyze slot utilization and identify issues
        
        Args:
            slots_df: DataFrame with slot data (required columns: AVAILABLE_OPENINGS, PROV_NAME, etc.)
            appointments_df: DataFrame with appointment data (optional, for deeper analysis)
        
        Returns:
            dict: Analysis results
        """
        
        # Basic metrics
        total_slots = len(slots_df)
        open_slots = len(slots_df[slots_df['AVAILABLE_OPENINGS'] > 0])
        open_percentage = (open_slots / total_slots * 100) if total_slots > 0 else 0
        
        # Provider utilization
        if 'NUM_APTS_SCHEDULED' in slots_df.columns and 'ORG_REG_OPENINGS' in slots_df.columns:
            slots_df['Utilization'] = slots_df['NUM_APTS_SCHEDULED'] / slots_df['ORG_REG_OPENINGS']
            avg_utilization = slots_df['Utilization'].mean()
        else:
            # Fallback calculation
            slots_df['Utilization'] = 1 - (slots_df['AVAILABLE_OPENINGS'] / slots_df.get('ORG_REG_OPENINGS', 1))
            avg_utilization = slots_df['Utilization'].mean()
        
        # Provider-level analysis
        provider_analysis = []
        for provider in slots_df['PROV_NAME'].unique():
            prov_slots = slots_df[slots_df['PROV_NAME'] == provider]
            prov_open = len(prov_slots[prov_slots['AVAILABLE_OPENINGS'] > 0])
            prov_total = len(prov_slots)
            prov_util = prov_slots['Utilization'].mean()
            
            status = 'Underutilized' if prov_util < 0.7 else 'Well Utilized' if prov_util < 0.9 else 'Overutilized'
            
            provider_analysis.append({
                'provider': provider,
                'total_slots': prov_total,
                'open_slots': prov_open,
                'utilization': prov_util,
                'status': status
            })
        
        # Utilization issues analysis (if appointment data available)
        utilization_issues = {'weekend_slots': 0, 'timing_issues': 0, 'other': open_slots}
        potential_matches = 0
        avg_wait_days = 0
        
        if appointments_df is not None and 'AppointmentDateTime' in appointments_df.columns:
            future_appts = appointments_df[appointments_df['AppointmentDateTime'] > datetime.now()]
            potential_matches = len(future_appts)
            
            if len(future_appts) > 0:
                wait_days = (future_appts['AppointmentDateTime'] - datetime.now()).dt.days
                avg_wait_days = wait_days.mean()
            
            # Analyze open slots for issues
            open_slots_df = slots_df[slots_df['AVAILABLE_OPENINGS'] > 0]
            weekend_count = 0
            
            if 'SLOT_BEGIN_TIME' in slots_df.columns:
                for _, slot in open_slots_df.iterrows():
                    if hasattr(slot['SLOT_BEGIN_TIME'], 'weekday') and slot['SLOT_BEGIN_TIME'].weekday() >= 5:
                        weekend_count += 1
                
                utilization_issues = {
                    'weekend_slots': weekend_count,
                    'timing_issues': max(0, open_slots - weekend_count - 2),  # Estimate
                    'other': 2  # Small buffer for other issues
                }
        
        return {
            'timestamp': self.timestamp,
            'total_slots': total_slots,
            'open_slots': open_slots,
            'open_percentage': open_percentage,
            'avg_utilization': avg_utilization,
            'provider_analysis': provider_analysis,
            'utilization_issues': utilization_issues,
            'potential_matches': potential_matches,
            'avg_wait_days': avg_wait_days
        }
    
    def generate_report(self, analysis_results, report_title="OBD Utilization Report"):
        """Generate formatted report"""
        
        results = analysis_results
        report_lines = [
            f"{report_title}",
            "=" * len(report_title),
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Report ID: OBD_{results['timestamp']}",
            "",
            "📊 SUMMARY METRICS:",
            f"   Total Slots: {results['total_slots']:,}",
            f"   Open Slots: {results['open_slots']:,} ({results['open_percentage']:.1f}%)",
            f"   Average Utilization: {results['avg_utilization']:.1%}",
            f"   Potential Patient Matches: {results['potential_matches']:,}",
            f"   Average Patient Wait: {results['avg_wait_days']:.1f} days",
            "",
            "👨‍⚕️ PROVIDER UTILIZATION:",
        ]
        
        # Provider details
        underutilized = []
        well_utilized = []
        overutilized = []
        
        for prov in results['provider_analysis']:
            line = f"   {prov['provider']}: {prov['utilization']:.1%} ({prov['open_slots']} open of {prov['total_slots']} slots)"
            
            if prov['status'] == 'Underutilized':
                underutilized.append(line + " ⚠️")
            elif prov['status'] == 'Overutilized':
                overutilized.append(line + " 🔴")
            else:
                well_utilized.append(line + " ✅")
        
        # Add providers by status
        if underutilized:
            report_lines.extend(underutilized)
        if well_utilized:
            report_lines.extend(well_utilized)
        if overutilized:
            report_lines.extend(overutilized)
        
        # Utilization issues
        issues = results['utilization_issues']
        report_lines.extend([
            "",
            "🔍 WHY SLOTS AREN'T UTILIZED:",
            f"   Weekend Slots: {issues.get('weekend_slots', 0)} slots",
            f"   Timing Issues: {issues.get('timing_issues', 0)} slots",
            f"   Other Factors: {issues.get('other', 0)} slots",
            "",
            "💡 KEY INSIGHTS:",
            f"   • {results['open_slots']} open slots available for {results['potential_matches']} waiting patients",
            f"   • Main opportunity: {'Weekend scheduling' if issues.get('weekend_slots', 0) > 5 else 'Provider balancing'}",
            f"   • Potential wait time reduction: {results['avg_wait_days']:.0f} days",
            "",
            "🎯 RECOMMENDATIONS:",
        ])
        
        # Dynamic recommendations based on data
        recommendations = []
        
        if len(underutilized) > 0:
            recommendations.append("   • Focus on underutilized providers (marked ⚠️)")
        
        if issues.get('weekend_slots', 0) > 3:
            recommendations.append("   • Review weekend slot scheduling policies")
        
        if results['avg_wait_days'] > 30:
            recommendations.append("   • Prioritize patient-slot matching to reduce wait times")
        
        if results['open_percentage'] > 25:
            recommendations.append("   • High open slot percentage - investigate demand patterns")
        
        if not recommendations:
            recommendations.append("   • Continue monitoring utilization trends")
        
        report_lines.extend(recommendations)
        
        return '\n'.join(report_lines)
    
    def save_report(self, analysis_results, report_title="OBD Utilization Report", save_json=True):
        """Save report to files"""
        
        # Generate text report
        report_text = self.generate_report(analysis_results, report_title)
        
        # Save text report
        report_filename = f"OBD_Report_{analysis_results['timestamp']}.txt"
        report_path = self.output_dir / report_filename
        
        with open(report_path, 'w') as f:
            f.write(report_text)
        
        # Optionally save JSON data for further analysis
        if save_json:
            json_filename = f"OBD_Data_{analysis_results['timestamp']}.json"
            json_path = self.output_dir / json_filename
            
            # Convert numpy types to native Python types for JSON serialization
            json_data = {}
            for key, value in analysis_results.items():
                if isinstance(value, np.floating):
                    json_data[key] = float(value)
                elif isinstance(value, np.integer):
                    json_data[key] = int(value)
                elif isinstance(value, list):
                    json_data[key] = value
                else:
                    json_data[key] = value
            
            with open(json_path, 'w') as f:
                json.dump(json_data, f, indent=2, default=str)
        
        return report_path, json_path if save_json else None

def quick_report(slots_df, appointments_df=None, output_dir="obd_reports", title="OBD Utilization Report"):
    """
    Quick function to generate OBD report from DataFrames
    
    Usage:
        report_path = quick_report(slots_df, appointments_df)
        print(f"Report saved to: {report_path}")
    """
    
    reporter = OBDReporter(output_dir)
    analysis = reporter.analyze_utilization(slots_df, appointments_df)
    report_path, json_path = reporter.save_report(analysis, title)
    
    print(f"📊 OBD Report Generated:")
    print(f"   Text Report: {report_path}")
    if json_path:
        print(f"   Data Export: {json_path}")
    
    return report_path

# Example usage for integration into existing OBD workflows
if __name__ == '__main__':
    # This would be called from your main OBD simulation
    print("OBD Reporter - Ready for integration")
    print("Usage: from obd_reporter import quick_report")
    print("       report_path = quick_report(slots_df, appointments_df)")
