 #!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Thu Jun 19 11:17:52 2025

@author: armang<PERSON><PERSON>, em<PERSON><PERSON><PERSON>

This script contains functions used for automatic processing of daily slot and
appointment history data pulled for use in automating the process of searching
for open slots and finding eligible patients to call via Operation Bird Dog.

"""

import json
import random
from ast import literal_eval
import math
from datetime import date, time, datetime, timedelta
import pandas as pd
import numpy as np


class OBDScheduler:
    def __init__(self):
        return

    def initialize_input_vars(self, input_filename):
        with open(input_filename, 'r') as infile:
            self.args = json.loads(infile.read())
        
        today = datetime.today().strftime("%Y%m%d")
        
        # Minimum numbers of days in between the original appointment and the new appointment (for example,
        # if this value is set to 7, then you can only reschedule a patient into a slot if you are moving them
        # up at least 7 days)
        self.min_day_apart = timedelta(days=int(self.args['min_day_apart']))
        
        
        # Maximum number of days out from the current date you are looking at in terms of open slots (for example,
        # if this value is set to 28, you will be looking for open slots for the next 4 weeks)
        self.max_slot_horizon = timedelta(days=int(self.args['max_slot_horizon']))
        
        # Allowed provider types to do OBD reschedules for
        self.provider_types = [ptype.strip() for ptype in self.args['provider_types'].split(',')]
        
        # Allowed provider types to cross schedule NP appointments
        self.allowed_cross_schedule_types = [ptype.strip() for ptype in self.args['allowed_cross_schedule_types'].split(',')]
        
        
        # Allow rescheduling into NP urgents
        self.allow_urgents = (self.args['allow_urgents'].lower())
        
        # How close to the np urgent the current date must be within for it to become accessible for OBD rescheduling
        self.urgent_time_horizon = int(self.args['urgent_time_horizon'])
        self.urgent_time_horizon = timedelta(days=self.urgent_time_horizon)

        # Allow OBD rescheduling into unblocked time
        self.allow_unblocked_slots = (self.args['allow_unblocked_slots'].lower())
        
        # Allow OBD rescheduling into RV slots
        self.allow_np_in_rv = (self.args['allow_np_in_rv'].lower())
        
        # How close to the RV the current date must be within for it to become accessible for OBD rescheduling
        self.rv_time_horizon = int(self.args['urgent_time_horizon'])
        self.rv_time_horizon = timedelta(days=self.rv_time_horizon)
        
        # Inclusion and exclusion list of slots that are clinic specific that must be specifically dropped or
        # included (example: PMR does not like rescheduling of NP Cranials (exclude "Cranial"), and TC Neuro allows NP Epilepsy into
        # generic open Epilepsy (include "Epilepsy")
        self.include_slots_like = [ptype.strip().lower() for ptype in self.args['include_slots_like'].split(',')]
        self.exclude_slots_like = [ptype.strip().lower() for ptype in self.args['exclude_slots_like'].split(',')]

        # Referral filtering - exclude appointments that are referrals
        # Common referral indicators in VisitType field
        self.referral_keywords = ['ref', 'referral', 'refer', 'consultation', 'consult']
        
        # #fix, the directory is screwing it up
        # # Slot and appointment data
        # slot_data_name = self.args['slot_data']
        # appt_data_name = self.args['appt_data']
        
        # slot_data_name = f"{today}_{slot_data_name}"
        # appt_data_name = f"{today}_{appt_data_name}"
        
        
        today_str = datetime.today().strftime("%Y%m%d")
        
        slot_directory = f"/Volumes/cheps-research/OBD/OBD Daily Pulls/Unprocessed Slot Data/{today_str}_OBD_Slot Data.csv"
        appt_directory = f"/Volumes/cheps-research/OBD/OBD Daily Pulls/Unprocessed Appt Data/{today_str}_OBD_Appt Data.csv"
        
        self.slot_data = pd.read_csv(slot_directory, low_memory=False)
        self.appt_data = pd.read_csv(appt_directory, low_memory=False)
        
    3
    
    # TO DO: all the data fields are going to need to be updated with the final pull
    # TO DO: confirm RVs and Urgent scheduling is working for a dept that has them
    
    #TO DO: add logic to allow user inputted exclusion criteria (e.g. NP Cranial)
    #TO DO: add logic to allow user inputter inclusion crtieria (e.g. Eplilepsy for NP Epilepsy)
        # for non match inclusion need to strip NP to see if type matches, and also check for visit length vs. slot length
    
    def process_schedule(self, dept_id, clinic_name):   
        
        today = datetime.combine(date.today(), time.min)
        
        # filter for slots and appts in the correct deprattment
        clinic_slots = self.slot_data[self.slot_data["DEPARTMENT_ID"] == dept_id]
        clinic_appts = self.appt_data[self.appt_data["DepartmentEpicId"] == dept_id]
        
        clinic_appts["AppointmentLengthInMinutes"] = clinic_appts["AppointmentLengthInMinutes"].astype(int)
        clinic_appts["PatientMrn"] = clinic_appts["PatientMrn"].apply(
            lambda mrn: str(mrn).zfill(9) if pd.notnull(mrn) else ""
        )
        
       # Convert all datetime columns into datetime format
        clinic_appts["AppointmentCreationDate"] = pd.to_datetime(
            clinic_appts["AppointmentCreationDate"], errors='coerce'
        )
        
        clinic_appts["AppointmentInstant"] = pd.to_datetime(
            clinic_appts["AppointmentInstant"], errors='coerce'
        )
        
        clinic_slots["SLOT_BEGIN_TIME"] = pd.to_datetime(
            clinic_slots["SLOT_BEGIN_TIME"], errors='coerce'
        )
        
        clinic_slots["SLOT_END_TIME"] = pd.to_datetime(
            clinic_slots["SLOT_END_TIME"], errors='coerce'
        )
        
        # Filter for only slots occuring after the current date
        clinic_slots = clinic_slots[clinic_slots["SLOT_BEGIN_TIME"].dt.date > pd.Timestamp.now().date()]
        
        # Filter for only slots occurring within the slot horizon
        clinic_slots = clinic_slots[clinic_slots["SLOT_BEGIN_TIME"].dt.date <= (pd.Timestamp.now().date() + self.max_slot_horizon)]
        
        # create copy of slot name column        
        clinic_slots["SLOT_BLOCKS"] = clinic_slots["BLOCK_NAME"]
        
        # Preserve an unfiltered (but cleaned up) copy of the slot data
        clinic_slots_old = clinic_slots.copy()
        
        # Create a separate dataframe that calculates utilization for each provider per day, and removes slots as OBD eligible if the
        # provider is overbooked that day
        clinic_slots["Date"] = clinic_slots["SLOT_BEGIN_TIME"].dt.date
        
        clinic_utilization_by_provider = clinic_slots.groupby(["PROV_ID", "Date"])[["NUM_APTS_SCHEDULED", "ORG_REG_OPENINGS"]].sum().reset_index()
        clinic_utilization_by_provider["Daily Utilization"] = clinic_utilization_by_provider["NUM_APTS_SCHEDULED"] / clinic_utilization_by_provider["ORG_REG_OPENINGS"]
        
        overbooked_providers = clinic_utilization_by_provider[clinic_utilization_by_provider["Daily Utilization"] >= 0.95]
        
        clinic_slots = clinic_slots[~clinic_slots.set_index(["PROV_ID", "Date"]).index.isin(overbooked_providers.set_index(["PROV_ID", "Date"]).index)]

        # filter for slots with standard openings 
        clinic_slots = clinic_slots[clinic_slots["NUM_APTS_SCHEDULED"] == 0]
        clinic_slots = clinic_slots[clinic_slots["ORG_REG_OPENINGS"]>0]
                
        # filter for slots with an allowable rescheduable provider type 
        clinic_slots = clinic_slots[clinic_slots["PROV_TYPE"].isin(self.provider_types)]
        clinic_slots = clinic_slots[clinic_slots["SLOT_BEGIN_TIME"]>=today]
        
        # filter out slots that are Second Opinion
        clinic_slots = clinic_slots[
            ~clinic_slots["SLOT_BLOCKS"].fillna("").str.lower().str.contains("opinion")
        ] 
        
        # filter out slots that are continued
        clinic_slots = clinic_slots[
            ~clinic_slots["SLOT_BLOCKS"].fillna("").str.lower().str.contains("continued")
        ]
        
        # filter out slots that are extended
        clinic_slots = clinic_slots[
            ~clinic_slots["SLOT_BLOCKS"].fillna("").str.lower().str.contains("extended")
        ]
        
        # rename any empty blocked slots to be unblocked to avoid any nan issues later
        clinic_slots.loc[clinic_slots["SLOT_BLOCKS"] == "", "SLOT_BLOCKS"] = "UNBLOCKED"
        clinic_slots.loc[pd.isna(clinic_slots["SLOT_BLOCKS"]), "SLOT_BLOCKS"] = "UNBLOCKED"
        
        for exclusion in self.exclude_slots_like:
            if not exclusion == "":
                clinic_slots = clinic_slots[~clinic_slots["SLOT_BLOCKS"].str.lower().str.contains(exclusion)]

        # if RV scheduling is allowed, include RV appts in the list of eligible slots
        if self.allow_np_in_rv == "y":
            # convert include_slots_like list to lowercase
            include_keywords = [kw.lower() for kw in self.include_slots_like]
                
            clinic_slots = clinic_slots[
                (
                    (clinic_slots["SLOT_BLOCKS"].str.lower().str.contains("np")) |
                    (
                        clinic_slots["SLOT_BLOCKS"].str.lower().str.contains("rv") & 
                        ((clinic_slots["SLOT_BEGIN_TIME"] - today) <= self.rv_time_horizon)
                    ) |
                    (clinic_slots["SLOT_BLOCKS"] == "UNBLOCKED") |
                    (
                        clinic_slots["SLOT_BLOCKS"].str.lower().apply(
                            lambda val: isinstance(val, str) and any(keyword.lower() in val for keyword in include_keywords)
                        ) & ((clinic_slots["SLOT_BEGIN_TIME"] - today) <= self.rv_time_horizon)
                    )
                )
            ]

    
        # if RV scheduling is not allowed, remove them
        else:
            clinic_slots = clinic_slots[
                (clinic_slots["SLOT_BLOCKS"].str.lower().str.contains("np")) |
                (clinic_slots["SLOT_BLOCKS"] == "UNBLOCKED")
            ]

        # if scheduling in NP urgents is allowed, filter for only ones within the allowed time horizon    
        if self.allow_urgents == "y":
            clinic_slots = clinic_slots[
                ~(
                    (clinic_slots["SLOT_BLOCKS"].str.lower().str.contains("urgent")) &
                    ((clinic_slots["SLOT_BEGIN_TIME"] - today) > self.urgent_time_horizon)
                )
            ]           

        # if scheduling in NP urgents is not allowed, filter them out of the eligible slot set
        else:
            clinic_slots = clinic_slots[
                ~(clinic_slots["SLOT_BLOCKS"].str.lower().str.contains("urgent"))
            ]
        

        # if scheduling in unblocked slots is not allowed, remove them from the set (otherwise, they are included by default)
        if self.allow_unblocked_slots == "n":
            clinic_slots = clinic_slots[~(clinic_slots["SLOT_BLOCKS"] == "UNBLOCKED")]
        
        # convert provider id to numeric to fish out errors
        clinic_appts["PrimaryVisitProviderEpicID"] = pd.to_numeric(clinic_appts["PrimaryVisitProviderEpicID"], errors='coerce')
        clinic_slots["PROV_ID"] = pd.to_numeric(clinic_slots["PROV_ID"], errors='coerce')
        clinic_slots_old["PROV_ID"] = pd.to_numeric(clinic_slots_old["PROV_ID"], errors='coerce')

        
        # drop rows where PROV_ID couldn't be converted
        clinic_appts = clinic_appts.dropna(subset=["PrimaryVisitProviderEpicID"])
        clinic_slots = clinic_slots.dropna(subset=["PROV_ID"])
        clinic_slots_old = clinic_slots_old.dropna(subset=["PROV_ID"])


        clinic_appts["PrimaryVisitProviderEpicID"] = clinic_appts["PrimaryVisitProviderEpicID"].astype(int)
        clinic_slots["PROV_ID"] = clinic_slots["PROV_ID"].astype(int)
        clinic_slots_old["PROV_ID"] = clinic_slots_old["PROV_ID"].astype(int)

        
        clinic_appts["DepartmentEpicId"] = clinic_appts["DepartmentEpicId"].astype(int)
        clinic_slots["DEPARTMENT_ID"] = clinic_slots["DEPARTMENT_ID"].astype(int)
        clinic_slots_old["DEPARTMENT_ID"] = clinic_slots_old["DEPARTMENT_ID"].astype(int)

        # merge the appts with the slots so that each appointment has an assigned slot        
        clinic_slot_apt_merge = clinic_appts.merge(clinic_slots_old, left_on=["AppointmentInstant", "PrimaryVisitProviderEpicID"], right_on=["SLOT_BEGIN_TIME", "PROV_ID"], how="left")
        
        # by current wait time
        #clinic_slot_apt_merge["Wait_Time"] = today - clinic_slot_apt_merge["AppointmentCreationDate"] 
        
        # by total expected wait time
        clinic_slot_apt_merge["Wait_Time"] = clinic_slot_apt_merge["AppointmentInstant"] - clinic_slot_apt_merge["AppointmentCreationDate"] 

        slot_store_list = pd.DataFrame(columns = [
            "DEPT_ID", "PROV_ID", "PROV_NAME", "PROV_TYPE", "SLOT_DATE", "SLOT_TIME",
            "SLOT_DAY_OF_WEEK", "SLOT_BEGIN_TIME", "SLOT_BLOCKS", "SLOT_LENGTH_MINUTES",
            "NUM_ELIGIBLE_PATIENTS", "AVG_PATIENT_WAIT_DAYS", "MAX_PATIENT_WAIT_DAYS",
            "MIN_PATIENT_WAIT_DAYS", "PROVIDER_AVAILABLE_CAPACITY", "Patient_MRNs"
        ])
        
        mrn_store_list = []
        
        # for each eligible slot
        for s, slot in clinic_slots.iterrows():
            slot_begin_time = slot["SLOT_BEGIN_TIME"]
            slot_end_time = slot["SLOT_END_TIME"]
            slot_provider_type = slot["PROV_TYPE"]
            slot_provider_id = slot["PROV_ID"]
            slot_type = slot["SLOT_BLOCKS"]
            slot_dept_id = slot["DEPARTMENT_ID"]
            slot_provider_name = slot["PROV_NAME"]
            slot_provider_type= slot["PROV_TYPE"]
            
            slot_length = int((slot_end_time - slot_begin_time).total_seconds() / 60)
            slot_date = slot_begin_time.date()

            # Check provider's current utilization for this specific date
            provider_day_utilization = clinic_utilization_by_provider[
                (clinic_utilization_by_provider["PROV_ID"] == slot_provider_id) &
                (clinic_utilization_by_provider["Date"] == slot_date)
            ]

            # Skip this slot if provider is already at capacity for this day
            if not provider_day_utilization.empty:
                current_utilization = provider_day_utilization["Daily Utilization"].iloc[0]
                available_capacity = provider_day_utilization["ORG_REG_OPENINGS"].iloc[0] - provider_day_utilization["NUM_APTS_SCHEDULED"].iloc[0]

                # Skip if no capacity available (already at 100% or higher)
                if available_capacity <= 0:
                    continue
            else:
                # If no utilization data, assume capacity is available
                available_capacity = 1

            # filter for patients whose original appointment is after the slot (later date)
            potential_patients = clinic_slot_apt_merge[clinic_slot_apt_merge["AppointmentInstant"]> (slot_begin_time + self.min_day_apart)]
            
            # filter for patients who have an appointment length less than or equal to the slot length
            potential_patients = potential_patients[potential_patients["AppointmentLengthInMinutes"] <= slot_length]
            
            # filter for patients who are being seen for an NP appointment
            potential_patients = potential_patients[potential_patients["VisitType"].fillna("").str.lower().str.contains("np")]

            # filter for patients who are being seen for an NP appointment
            potential_patients = potential_patients[~(potential_patients["VisitType"].fillna("").str.lower().str.contains("opinion"))]

            # Filter out referral appointments based on VisitType
            for referral_keyword in self.referral_keywords:
                potential_patients = potential_patients[~(potential_patients["VisitType"].fillna("").str.lower().str.contains(referral_keyword))]
                        
            # filter for patients whose appointment provider type is the same as the slot
            potential_patients = potential_patients[potential_patients["PrimaryVisitProviderType"]==slot_provider_type]

            #if cross scheduling is allowed for that provider type
            if slot_provider_type in self.allowed_cross_schedule_types:
                
                #filter for only appts of the same block type as the slot
                potential_patients = potential_patients[((potential_patients["PrimaryVisitProviderType"] == slot_provider_type)&(potential_patients["SLOT_BLOCKS"]==slot_type)) | 
                                                        (potential_patients["PROV_ID"]==slot_provider_id)]
            
            # if cross scheduling is not allowed: 
            else:
                potential_patients = potential_patients[(potential_patients["PROV_ID"]==slot_provider_id)]
                
            
            # if the slot type is not an RV slot
            if "rv" not in slot_type.lower():
                include_keywords = self.include_slots_like
            
                potential_patients = potential_patients[
                    (potential_patients["SLOT_BLOCKS"] == slot_type) |
                    (potential_patients["SLOT_BLOCKS"].str.lower().str.contains("urgent")) |
                    (potential_patients["SLOT_BLOCKS"].fillna("").str.lower().apply(
                        lambda val: any(keyword in val for keyword in include_keywords)
                    ))
                ]

            # if the slot is a video slot
            if "video" in slot_type.lower():
                # filter for only appts for patients also scheduled in video slots
                potential_patients = potential_patients[potential_patients["SLOT_BLOCKS"].fillna("").str.lower().str.contains("video", na=False)]
                
            # if the slot is in person
            else:
                # filter for all in person slots
                potential_patients = potential_patients[~(potential_patients["SLOT_BLOCKS"].fillna("").str.lower().str.contains("video"))]
            
            # Add reference columns to see if the patient original slot matches the new slot (not required but prioritized over
            # a non match)
            potential_patients["Slot_Match"] = potential_patients["SLOT_BLOCKS"] == slot_type
            
            # Add reference columns to see if the patient original provider matches the new provider (not required but prioritized over
            # a non match)
            potential_patients["Prov_Match"] = potential_patients["PROV_ID"] == slot_provider_id
            
            # Sort patients by priority (first by waiting time, then by correct provider matching, then by block matching)
            potential_patients = potential_patients.sort_values(by=["Wait_Time","PROV_ID", "SLOT_BLOCKS"]).reset_index(drop=True)

            # Limit the number of patients suggested based on available capacity
            # This ensures we don't suggest more patients than the provider can handle
            if len(potential_patients) > available_capacity:
                potential_patients = potential_patients.head(int(available_capacity))

            # Get a list of patient MRNs (used for reference later to manually search up patient demographics in Epic of people who are
            # accepting/denying reschedules)
            potential_patients_mrn_list = potential_patients["PatientMrn"].unique()

            potential_patients_mrn_list = "[" + ", ".join(str(mrn) for mrn in potential_patients["PatientMrn"].unique()) + "]"

            potential_patients_encounter_id_list = potential_patients["EncounterEpicCsn"].unique()
            
            # Convert MRN to a dataframe
            mrn_store_list.extend(potential_patients_encounter_id_list)
               
            # Calculate additional helpful information
            slot_date = slot_begin_time.strftime("%Y-%m-%d")
            slot_time = slot_begin_time.strftime("%H:%M")
            slot_day_of_week = slot_begin_time.strftime("%A")
            num_eligible_patients = len(potential_patients)

            # Get patient wait times for context
            if not potential_patients.empty:
                avg_wait_days = potential_patients["Wait_Time"].dt.days.mean()
                max_wait_days = potential_patients["Wait_Time"].dt.days.max()
                min_wait_days = potential_patients["Wait_Time"].dt.days.min()
            else:
                avg_wait_days = max_wait_days = min_wait_days = 0

            # Record all the information needed for OBD student to identify slot in chart, and add the list of patients (by MRN) that they
            # should look up to try and fill this open slot
            slot_row = pd.DataFrame({
                "DEPT_ID": [slot_dept_id],
                "PROV_ID": [slot_provider_id],
                "PROV_NAME": [slot_provider_name],
                "PROV_TYPE": [slot_provider_type],
                "SLOT_DATE": [slot_date],
                "SLOT_TIME": [slot_time],
                "SLOT_DAY_OF_WEEK": [slot_day_of_week],
                "SLOT_BEGIN_TIME": [slot_begin_time],
                "SLOT_BLOCKS": [slot_type],
                "SLOT_LENGTH_MINUTES": [slot_length],
                "NUM_ELIGIBLE_PATIENTS": [num_eligible_patients],
                "AVG_PATIENT_WAIT_DAYS": [round(avg_wait_days, 1) if avg_wait_days > 0 else 0],
                "MAX_PATIENT_WAIT_DAYS": [max_wait_days],
                "MIN_PATIENT_WAIT_DAYS": [min_wait_days],
                "PROVIDER_AVAILABLE_CAPACITY": [int(available_capacity)],
                "Patient_MRNs": [potential_patients_mrn_list]  # already a list keep it wrapped in a list of one element
            })
            
            # Add this slot to the dataset of all sets from the dept that need to be rescheduled
            slot_store_list_copy = slot_store_list.copy()
            slot_store_list = pd.concat([slot_store_list_copy, slot_row]).reset_index(drop=True)
       
        
        encounter_list = list(dict.fromkeys(mrn_store_list))
        
        encounter_df = clinic_slot_apt_merge[
            clinic_slot_apt_merge["EncounterEpicCsn"].isin(encounter_list)
            ]
 
        encounter_df = encounter_df[[
            "EncounterEpicCsn",
            "PatientMrn",
            "Wait_Time",
            "PROV_ID",
            "PROV_NAME",
            "SLOT_BLOCKS"
        ]].sort_values(by="Wait_Time").reset_index(drop=True)
        
        slot_store_list.sort_values(by="SLOT_BEGIN_TIME").reset_index(drop=True)
        
        encounter_df["PatientMrn"] = encounter_df["PatientMrn"].apply(
            lambda x: str(x).zfill(9) if len(str(x)) == 8 else str(x)
        )
        
        # Return the list of slots with assigned patients, as well as the patient MRN list
        return slot_store_list, encounter_df, clinic_slots_old, self.max_slot_horizon
    
    
    # TO DO: may need to generalize a bit or split into additional functions to use to combine consecutive "Continued" type slots
    # TO DO: test this with a dept that actually has designated RV slots
    def merge_consecutive_RV_slots(self, clinic_slots):
        potential_slots_grouped = pd.DataFrame(clinic_slots)
        potential_slots_grouped = potential_slots_grouped.sort_values(
            by=['Provider ID', 'SLOT_BEGIN_TIME']
        ).reset_index(drop=True)
        
        # filter for RV rows
        rv_rows = potential_slots_grouped[potential_slots_grouped['SLOT_BLOCKS'].str.lower().str.contains("rv", na=False)].copy()
        non_rv_rows = potential_slots_grouped[~potential_slots_grouped['SLOT_BLOCKS'].str.lower().str.contains("rv", na=False)].copy()
        
        # assign block groups within RV rows only
        rv_rows['block_group'] = (
            (rv_rows['SLOT_BEGIN_TIME'] != rv_rows['SLOT_END_TIME'].shift()) |
            (rv_rows['Provider ID'] != rv_rows['Provider ID'].shift())
        ).cumsum()
        
        # group the RV rows
        rv_grouped = (
            rv_rows.groupby(['Provider ID', 'block_group'])
            .agg({
                'SLOT_BEGIN_TIME': 'min',
                'SLOT_END_TIME': 'max',
                'SLOT_BLOCKS': list
            })
            .reset_index(drop=True)
        )
        
        # combine grouped RV and ungrouped non-RV
        potential_slots_grouped = pd.concat([rv_grouped, non_rv_rows], ignore_index=True)
        
        # final sort/shuffle (optional)
        potential_slots_grouped = potential_slots_grouped.sort_values(by="SLOT_BEGIN_TIME").reset_index(drop=True)
        
        return potential_slots_grouped
    
    
