# Operation Bird Dog (OBD) System Improvements

## Overview
This document outlines the improvements made to the Operation Bird Dog system to address output clarity, referral handling, session limits, and eligibility statistics.

## Key Improvements Implemented

### 1. Referral Filtering
**Problem**: The system was not filtering out referral appointments, which should be ignored.

**Solution**: 
- Added automatic filtering of referral appointments based on VisitType field
- Filters out appointments containing keywords: 'ref', 'referral', 'refer', 'consultation', 'consult'
- Applied during patient eligibility checking in `obd_init.py`

**Code Location**: Lines 75, 314-316 in `obd_init.py`

### 2. Enhanced Output Clarity
**Problem**: Output was minimal and not user-friendly.

**Solution**: Enhanced the slot opportunities output with additional informative columns:
- `SLOT_DATE`: Human-readable date (YYYY-MM-DD)
- `SLOT_TIME`: Time in HH:MM format
- `SLOT_DAY_OF_WEEK`: Day name (Monday, Tuesday, etc.)
- `SLOT_LENGTH_MINUTES`: Duration of the slot
- `NUM_ELIGIBLE_PATIENTS`: Count of patients who could fill this slot
- `AVG_PATIENT_WAIT_DAYS`: Average wait time for eligible patients
- `MAX_PATIENT_WAIT_DAYS`: Longest wait time among eligible patients
- `MIN_PATIENT_WAIT_DAYS`: Shortest wait time among eligible patients
- `PROVIDER_AVAILABLE_CAPACITY`: How many additional patients the provider can handle

**Benefits**: 
- Users can quickly understand slot timing and context
- Wait time information helps prioritize which patients to call first
- Capacity information prevents overbooking

### 3. Enhanced Session Limit Validation
**Problem**: System only removed slots from overbooked providers but didn't validate capacity when suggesting patients.

**Solution**: 
- Added real-time capacity checking for each provider on each day
- Limits patient suggestions to available provider capacity
- Skips slots where providers have no remaining capacity
- Includes capacity information in output for transparency

**Benefits**:
- Prevents suggesting more patients than a provider can handle
- Reduces wasted effort calling patients who can't be accommodated
- Maintains provider schedule integrity

### 4. Comprehensive Eligibility Statistics
**Problem**: No visibility into why patients couldn't be moved to open slots.

**Solution**: Created comprehensive analytics module that tracks:

#### Basic OBD Metrics:
- Total open slots found
- Total eligible patients
- Average patient wait times
- Provider utilization statistics
- Slot type and provider type breakdowns
- Capacity constraints analysis

#### Patient Eligibility Analysis:
Tracks why future appointments cannot be moved to open slots:
- **Insufficient Time Gap**: Not enough days between current and new appointment
- **Appointment Too Long**: Patient's appointment longer than available slot
- **Provider Type Mismatch**: Patient needs different type of provider
- **Slot Type Mismatch**: Patient's appointment type doesn't match slot type
- **Not NP Appointment**: Patient not scheduled for Nurse Practitioner visit
- **Referral Appointment**: Patient has referral appointment (now filtered out)
- **Opinion Appointment**: Patient scheduled for second opinion

#### Output Reports:
1. **JSON Report**: Detailed machine-readable analysis (`{dept}_obd_analysis_report.json`)
2. **Summary Report**: Human-readable summary (`{dept}_obd_summary_report.txt`)

## Notes Data Access Investigation

**Finding**: The current system does **NOT** have access to appointment notes or free-text fields. 

**Available Data Fields**:
- Structured appointment data (times, providers, departments)
- Visit types and appointment lengths
- Patient MRNs and encounter IDs
- Provider utilization metrics

**Recommendation**: If referral information is primarily in notes fields not currently available, consider:
1. Adding notes fields to the data pull if possible
2. Using alternative indicators (appointment types, provider referral patterns)
3. Manual review processes for edge cases

## Usage Instructions

### Running the Enhanced System
The system now automatically generates comprehensive reports when run:

```bash
python obd_main.py
```

### Output Files Generated (per department):
1. `{dept}_slot_reschedule_opportunities.csv` - Enhanced slot opportunities with new columns
2. `{dept}_mrn_list.csv` - Patient list (unchanged)
3. `{dept}_all_slots_data.csv` - All slots data (unchanged)
4. `{dept}_obd_analysis_report.json` - Detailed analytics (NEW)
5. `{dept}_obd_summary_report.txt` - Human-readable summary (NEW)

### Key Metrics to Monitor
- **Utilization rates**: Track provider capacity usage
- **Eligibility barriers**: Understand why patients can't be moved
- **Wait times**: Prioritize patients with longest waits
- **Capacity constraints**: Identify bottlenecks

## Configuration Options

All existing configuration options remain unchanged. The referral filtering is automatic and doesn't require configuration changes.

## Benefits Summary

1. **Improved User Experience**: Clearer, more informative output
2. **Better Resource Management**: Prevents provider overbooking
3. **Data-Driven Insights**: Understanding of system constraints and opportunities
4. **Reduced Wasted Effort**: Filtering out inappropriate appointments
5. **Enhanced Reporting**: Comprehensive analytics for continuous improvement

## Future Enhancements

1. **Notes Integration**: If notes data becomes available, enhance referral detection
2. **Predictive Analytics**: Use historical data to predict successful reschedule rates
3. **Real-time Capacity Updates**: Dynamic capacity management
4. **Patient Preference Integration**: Consider patient scheduling preferences
5. **Automated Prioritization**: AI-driven patient ranking for optimal outcomes
