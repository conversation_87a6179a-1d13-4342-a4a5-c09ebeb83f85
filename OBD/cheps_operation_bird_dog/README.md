# Simple OBD Test

One simple file that creates test data, analyzes it, and shows results with charts.

## Usage

```bash
python obd_test.py
```

## What It Does

1. **Creates Test Data**: Generates realistic slots and appointments
2. **Analyzes Data**: Calculates key OBD metrics
3. **Creates Charts**: 4-panel visualization showing:
   - Slot availability (pie chart)
   - Provider utilization (bar chart with color coding)
   - Key metrics (bar chart)
   - Wait time distribution (histogram)
4. **Saves Results**: CSV files and summary report

## Output Files

- `obd_results.png` - Charts and visualizations
- `test_slots.csv` - Generated slot data
- `test_appointments.csv` - Generated appointment data  
- `report.txt` - Summary report

## Sample Results

```
📊 Total Slots: 88
🟢 Open Slots: 27 (30.7%)
👥 Total Appointments: 30
📅 Future Appointments: 20
📈 Avg Utilization: 69.3%
⏰ Avg Wait Days: 36.8
```

## Key Metrics

- **Slot Availability**: Percentage of open vs booked slots
- **Provider Utilization**: Color-coded utilization rates (red >90%, orange >80%, green <80%)
- **Wait Times**: Average days patients wait for appointments
- **Future Appointments**: Appointments that could potentially be moved

This gives you everything you need to understand OBD data and see what the metrics look like, all in one simple file that always works.
