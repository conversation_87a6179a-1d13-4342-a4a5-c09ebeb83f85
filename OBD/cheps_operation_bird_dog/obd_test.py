#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple OBD Test

Creates test data, runs analysis, shows metrics, and creates charts.
Everything you need in one simple file.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from pathlib import Path

def create_test_data():
    """Create simple test data for OBD"""
    
    np.random.seed(42)  # Reproducible results
    
    # Create slots
    slots = []
    base_date = datetime.now() + timedelta(days=1)
    
    providers = [
        {'id': 'PROV001', 'name': 'Dr. <PERSON>', 'type': 'Physician'},
        {'id': 'PROV002', 'name': 'N<PERSON> Jones', 'type': 'Nurse Practitioner'},
        {'id': 'PROV003', 'name': 'Dr. <PERSON>', 'type': 'Physician'}
    ]
    
    # Generate slots for 10 days
    for day in range(10):
        for provider in providers:
            for slot in range(np.random.randint(2, 5)):  # 2-4 slots per provider per day
                slot_time = base_date + timedelta(days=day, hours=8+slot*2)
                is_open = np.random.random() > 0.7  # 30% open slots
                
                slots.append({
                    'DEPARTMENT_ID': 999001,
                    'PROV_ID': provider['id'],
                    'PROV_NAME': provider['name'],
                    'PROV_TYPE': provider['type'],
                    'SLOT_BEGIN_TIME': slot_time,
                    'SLOT_BLOCKS': 'General',
                    'SLOT_LENGTH_MINUTES': 30,
                    'ORG_REG_OPENINGS': 1,
                    'NUM_APTS_SCHEDULED': 0 if is_open else 1,
                    'AVAILABLE_OPENINGS': 1 if is_open else 0
                })
    
    # Create appointments
    appointments = []
    for i in range(30):
        provider = np.random.choice(providers)
        # Mix of future and past appointments
        if i < 20:  # 20 future
            appt_time = base_date + timedelta(days=np.random.randint(1, 60))
        else:  # 10 past
            appt_time = base_date - timedelta(days=np.random.randint(1, 30))
        
        appointments.append({
            'PatientMrn': f"{100000000 + i:09d}",
            'DepartmentEpicId': 999001,
            'ProviderId': provider['id'],
            'ProviderName': provider['name'],
            'ProviderType': provider['type'],
            'AppointmentDateTime': appt_time,
            'VisitType': 'General Visit',
            'AppointmentLengthInMinutes': 30,
            'EncounterId': f"ENC{i:06d}",
            'AppointmentStatus': 'Scheduled'
        })
    
    return pd.DataFrame(slots), pd.DataFrame(appointments)

def analyze_data(slots_df, appointments_df):
    """Analyze the test data and identify utilization issues"""

    # Basic metrics
    total_slots = len(slots_df)
    open_slots = len(slots_df[slots_df['AVAILABLE_OPENINGS'] > 0])
    open_percentage = (open_slots / total_slots * 100) if total_slots > 0 else 0

    total_appointments = len(appointments_df)
    future_appointments = len(appointments_df[appointments_df['AppointmentDateTime'] > datetime.now()])

    # Provider utilization
    slots_df['Utilization'] = slots_df['NUM_APTS_SCHEDULED'] / slots_df['ORG_REG_OPENINGS']
    avg_utilization = slots_df['Utilization'].mean()

    # Wait times
    future_appts = appointments_df[appointments_df['AppointmentDateTime'] > datetime.now()]
    if len(future_appts) > 0:
        wait_days = (future_appts['AppointmentDateTime'] - datetime.now()).dt.days
        avg_wait_days = wait_days.mean()
    else:
        avg_wait_days = 0

    # ANALYZE WHY SLOTS AREN'T UTILIZED
    open_slots_df = slots_df[slots_df['AVAILABLE_OPENINGS'] > 0].copy()

    # Categorize utilization issues
    utilization_issues = {
        'provider_mismatch': 0,
        'timing_issues': 0,
        'length_mismatch': 0,
        'department_mismatch': 0,
        'too_far_future': 0,
        'weekend_slots': 0
    }

    # Analyze each open slot to see why it might not be filled
    for _, slot in open_slots_df.iterrows():
        slot_date = slot['SLOT_BEGIN_TIME']

        # Check if it's a weekend (less likely to be filled)
        if slot_date.weekday() >= 5:  # Saturday = 5, Sunday = 6
            utilization_issues['weekend_slots'] += 1

        # Check if it's too far in the future (>30 days)
        days_ahead = (slot_date - datetime.now()).days
        if days_ahead > 30:
            utilization_issues['too_far_future'] += 1

        # Check if there are patients who could potentially use this slot
        eligible_patients = future_appts[
            (future_appts['DepartmentEpicId'] == slot['DEPARTMENT_ID']) &
            (future_appts['AppointmentLengthInMinutes'] <= slot['SLOT_LENGTH_MINUTES']) &
            (future_appts['AppointmentDateTime'] > slot_date + timedelta(days=7))  # Min 7 days apart
        ]

        if len(eligible_patients) == 0:
            # No eligible patients - could be various reasons
            dept_patients = future_appts[future_appts['DepartmentEpicId'] == slot['DEPARTMENT_ID']]
            if len(dept_patients) == 0:
                utilization_issues['department_mismatch'] += 1
            else:
                # Check if it's provider type or timing
                provider_match = dept_patients[dept_patients['ProviderType'] == slot['PROV_TYPE']]
                if len(provider_match) == 0:
                    utilization_issues['provider_mismatch'] += 1
                else:
                    utilization_issues['timing_issues'] += 1

    # Provider-level analysis
    provider_analysis = []
    for provider in slots_df['PROV_NAME'].unique():
        prov_slots = slots_df[slots_df['PROV_NAME'] == provider]
        prov_open = len(prov_slots[prov_slots['AVAILABLE_OPENINGS'] > 0])
        prov_total = len(prov_slots)
        prov_util = prov_slots['Utilization'].mean()

        provider_analysis.append({
            'provider': provider,
            'total_slots': prov_total,
            'open_slots': prov_open,
            'utilization': prov_util,
            'status': 'Underutilized' if prov_util < 0.7 else 'Well Utilized' if prov_util < 0.9 else 'Overutilized'
        })

    return {
        'total_slots': total_slots,
        'open_slots': open_slots,
        'open_percentage': open_percentage,
        'total_appointments': total_appointments,
        'future_appointments': future_appointments,
        'avg_utilization': avg_utilization,
        'avg_wait_days': avg_wait_days,
        'utilization_issues': utilization_issues,
        'provider_analysis': provider_analysis,
        'potential_matches': len(future_appts[future_appts['AppointmentDateTime'] > datetime.now() + timedelta(days=7)])
    }

def create_charts(slots_df, appointments_df, results, output_dir):
    """Create analysis charts including utilization issues"""

    # Create two sets of charts
    # Chart set 1: Overview
    fig1, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig1.suptitle('OBD Test Results - Overview', fontsize=16, fontweight='bold')

    # Chart 1: Slot availability
    labels = ['Open', 'Booked']
    sizes = [results['open_slots'], results['total_slots'] - results['open_slots']]
    colors = ['#2ecc71', '#e74c3c']
    ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%')
    ax1.set_title('Slot Availability')

    # Chart 2: Provider utilization
    provider_util = slots_df.groupby('PROV_NAME')['Utilization'].mean()
    bars = ax2.bar(range(len(provider_util)), provider_util.values,
                  color=['red' if u > 0.9 else 'orange' if u > 0.8 else 'green'
                        for u in provider_util.values])
    ax2.set_title('Provider Utilization')
    ax2.set_ylabel('Utilization Rate')
    ax2.set_xticks(range(len(provider_util)))
    ax2.set_xticklabels([name.split()[-1] for name in provider_util.index])
    ax2.axhline(y=0.8, color='orange', linestyle='--', alpha=0.7, label='80% Target')
    ax2.axhline(y=0.9, color='red', linestyle='--', alpha=0.7, label='90% High')
    ax2.legend()

    # Add utilization percentages on bars
    for bar, util in zip(bars, provider_util.values):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
               f'{util:.1%}', ha='center', va='bottom', fontweight='bold')

    # Chart 3: Key metrics
    metrics = ['Total Slots', 'Open Slots', 'Appointments', 'Potential Matches']
    values = [results['total_slots'], results['open_slots'],
             results['total_appointments'], results['potential_matches']]
    bars = ax3.bar(metrics, values, color=['#3498db', '#2ecc71', '#f39c12', '#9b59b6'])
    ax3.set_title('Key Metrics')
    ax3.set_ylabel('Count')
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
               str(value), ha='center', va='bottom', fontweight='bold')
    plt.setp(ax3.get_xticklabels(), rotation=45)

    # Chart 4: Wait time distribution
    future_appts = appointments_df[appointments_df['AppointmentDateTime'] > datetime.now()]
    if len(future_appts) > 0:
        wait_days = (future_appts['AppointmentDateTime'] - datetime.now()).dt.days
        ax4.hist(wait_days, bins=10, alpha=0.7, color='skyblue', edgecolor='black')
        ax4.axvline(wait_days.mean(), color='red', linestyle='--', linewidth=2,
                   label=f'Mean: {wait_days.mean():.1f} days')
        ax4.set_title('Wait Time Distribution')
        ax4.set_xlabel('Days')
        ax4.set_ylabel('Patients')
        ax4.legend()
    else:
        ax4.text(0.5, 0.5, 'No Future\nAppointments', ha='center', va='center',
                transform=ax4.transAxes, fontsize=12)
        ax4.set_title('Wait Time Distribution')

    plt.tight_layout()
    plt.savefig(output_dir / 'obd_results.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Chart set 2: Utilization Issues Analysis
    fig2, ((ax5, ax6), (ax7, ax8)) = plt.subplots(2, 2, figsize=(15, 12))
    fig2.suptitle('Why Slots Are Not Utilized - Root Cause Analysis', fontsize=16, fontweight='bold')

    # Chart 5: Utilization issues breakdown
    issues = results['utilization_issues']
    issue_labels = [
        'Provider\nMismatch',
        'Timing\nIssues',
        'Weekend\nSlots',
        'Too Far\nFuture',
        'Department\nMismatch'
    ]
    issue_values = [
        issues['provider_mismatch'],
        issues['timing_issues'],
        issues['weekend_slots'],
        issues['too_far_future'],
        issues['department_mismatch']
    ]

    colors = ['#e74c3c', '#f39c12', '#9b59b6', '#3498db', '#e67e22']
    bars = ax5.bar(issue_labels, issue_values, color=colors, alpha=0.8)
    ax5.set_title('Reasons Open Slots Are Not Filled')
    ax5.set_ylabel('Number of Slots')

    # Add value labels on bars
    for bar, value in zip(bars, issue_values):
        if value > 0:
            height = bar.get_height()
            ax5.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                   str(value), ha='center', va='bottom', fontweight='bold')

    # Chart 6: Provider status breakdown
    provider_status = {}
    for prov in results['provider_analysis']:
        status = prov['status']
        provider_status[status] = provider_status.get(status, 0) + 1

    if provider_status:
        status_labels = list(provider_status.keys())
        status_values = list(provider_status.values())
        status_colors = {'Underutilized': '#e74c3c', 'Well Utilized': '#2ecc71', 'Overutilized': '#f39c12'}
        colors = [status_colors.get(label, '#95a5a6') for label in status_labels]

        ax6.pie(status_values, labels=status_labels, colors=colors, autopct='%1.0f%%')
        ax6.set_title('Provider Utilization Status')

    # Chart 7: Provider comparison
    providers = [p['provider'].split()[-1] for p in results['provider_analysis']]
    open_slots_by_provider = [p['open_slots'] for p in results['provider_analysis']]
    utilizations = [p['utilization'] for p in results['provider_analysis']]

    ax7_twin = ax7.twinx()

    # Bar chart for open slots
    bars1 = ax7.bar([i-0.2 for i in range(len(providers))], open_slots_by_provider,
                   width=0.4, color='lightcoral', alpha=0.8, label='Open Slots')

    # Line chart for utilization
    line1 = ax7_twin.plot(range(len(providers)), utilizations, 'bo-',
                         color='darkblue', linewidth=2, markersize=8, label='Utilization')

    ax7.set_title('Provider Open Slots vs Utilization')
    ax7.set_xlabel('Providers')
    ax7.set_ylabel('Open Slots', color='red')
    ax7_twin.set_ylabel('Utilization Rate', color='blue')
    ax7.set_xticks(range(len(providers)))
    ax7.set_xticklabels(providers)

    # Add utilization threshold lines
    ax7_twin.axhline(y=0.8, color='orange', linestyle='--', alpha=0.7)
    ax7_twin.axhline(y=0.9, color='red', linestyle='--', alpha=0.7)

    # Chart 8: Opportunity summary
    opportunity_data = {
        'Total Open Slots': results['open_slots'],
        'Patients Waiting': results['future_appointments'],
        'Potential Matches': results['potential_matches'],
        'Avg Wait Days': int(results['avg_wait_days'])
    }

    labels = list(opportunity_data.keys())
    values = list(opportunity_data.values())
    colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12']

    bars = ax8.bar(labels, values, color=colors, alpha=0.8)
    ax8.set_title('OBD Opportunity Summary')
    ax8.set_ylabel('Count/Days')

    # Add value labels
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax8.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
               str(value), ha='center', va='bottom', fontweight='bold')

    plt.setp(ax8.get_xticklabels(), rotation=45)

    plt.tight_layout()
    plt.savefig(output_dir / 'utilization_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def save_results(slots_df, appointments_df, results, output_dir):
    """Save test data and results with detailed utilization analysis"""

    # Save data
    slots_df.to_csv(output_dir / 'test_slots.csv', index=False)
    appointments_df.to_csv(output_dir / 'test_appointments.csv', index=False)

    # Save detailed report
    issues = results['utilization_issues']
    report = [
        "OBD Test Report - Detailed Analysis",
        "=" * 40,
        f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "BASIC METRICS:",
        f"  Total Slots: {results['total_slots']}",
        f"  Open Slots: {results['open_slots']} ({results['open_percentage']:.1f}%)",
        f"  Total Appointments: {results['total_appointments']}",
        f"  Future Appointments: {results['future_appointments']}",
        f"  Potential Matches: {results['potential_matches']}",
        f"  Average Utilization: {results['avg_utilization']:.1%}",
        f"  Average Wait Days: {results['avg_wait_days']:.1f}",
        "",
        "WHY SLOTS ARE NOT UTILIZED:",
        f"  Provider Type Mismatch: {issues['provider_mismatch']} slots",
        f"  Timing Issues: {issues['timing_issues']} slots",
        f"  Weekend Slots: {issues['weekend_slots']} slots",
        f"  Too Far in Future: {issues['too_far_future']} slots",
        f"  Department Mismatch: {issues['department_mismatch']} slots",
        "",
        "PROVIDER ANALYSIS:",
    ]

    for prov in results['provider_analysis']:
        report.extend([
            f"  {prov['provider']}:",
            f"    - Total Slots: {prov['total_slots']}",
            f"    - Open Slots: {prov['open_slots']}",
            f"    - Utilization: {prov['utilization']:.1%}",
            f"    - Status: {prov['status']}",
            ""
        ])

    report.extend([
        "KEY INSIGHTS:",
        f"  • {results['open_slots']} open slots could potentially serve {results['potential_matches']} waiting patients",
        f"  • Main utilization barriers: {max(issues, key=issues.get)} ({max(issues.values())} slots)",
        f"  • Average patient wait time is {results['avg_wait_days']:.1f} days",
        "",
        "RECOMMENDATIONS:",
        "  • Focus on provider type matching for better utilization",
        "  • Consider patient preferences for timing",
        "  • Review weekend slot scheduling policies",
        "  • Optimize advance booking windows",
        "",
        "FILES CREATED:",
        "  - obd_results.png (overview charts)",
        "  - utilization_analysis.png (detailed utilization analysis)",
        "  - test_slots.csv (slot data)",
        "  - test_appointments.csv (appointment data)",
        "  - report.txt (this detailed report)"
    ])

    with open(output_dir / 'report.txt', 'w') as f:
        f.write('\n'.join(report))

def main():
    """Run the OBD test"""
    
    print("=" * 40)
    print("OBD TEST")
    print("=" * 40)
    
    # Setup
    output_dir = Path("obd_test_output")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # Step 1: Create test data
        print("\n1. Creating test data...")
        slots_df, appointments_df = create_test_data()
        print(f"   ✓ Created {len(slots_df)} slots and {len(appointments_df)} appointments")
        
        # Step 2: Analyze
        print("\n2. Analyzing data...")
        results = analyze_data(slots_df, appointments_df)
        
        # Step 3: Create charts
        print("\n3. Creating charts...")
        create_charts(slots_df, appointments_df, results, output_dir)
        print("   ✓ Created obd_results.png (overview)")
        print("   ✓ Created utilization_analysis.png (detailed analysis)")
        
        # Step 4: Save results
        print("\n4. Saving results...")
        save_results(slots_df, appointments_df, results, output_dir)
        print("   ✓ Saved data and report")

        # Step 5: Generate modular report (example integration)
        print("\n5. Generating modular report...")
        from obd_reporter import quick_report
        report_path = quick_report(slots_df, appointments_df, "obd_reports", "OBD Test Simulation Report")
        print("   ✓ Generated timestamped report")
        
        # Step 6: Show results
        print("\n" + "=" * 40)
        print("RESULTS")
        print("=" * 40)
        print(f"📊 Total Slots: {results['total_slots']}")
        print(f"🟢 Open Slots: {results['open_slots']} ({results['open_percentage']:.1f}%)")
        print(f"👥 Total Appointments: {results['total_appointments']}")
        print(f"📅 Future Appointments: {results['future_appointments']}")
        print(f"📈 Avg Utilization: {results['avg_utilization']:.1%}")
        print(f"⏰ Avg Wait Days: {results['avg_wait_days']:.1f}")
        
        print(f"\n📁 Files saved to: {output_dir}")
        print("✅ TEST COMPLETED")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
