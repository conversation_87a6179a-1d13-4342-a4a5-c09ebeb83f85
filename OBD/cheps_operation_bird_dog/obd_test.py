#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple OBD Test

Creates test data, runs analysis, shows metrics, and creates charts.
Everything you need in one simple file.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from pathlib import Path

def create_test_data():
    """Create simple test data for OBD"""
    
    np.random.seed(42)  # Reproducible results
    
    # Create slots
    slots = []
    base_date = datetime.now() + timedelta(days=1)
    
    providers = [
        {'id': 'PROV001', 'name': 'Dr. <PERSON>', 'type': 'Physician'},
        {'id': 'PROV002', 'name': 'N<PERSON> Jones', 'type': 'Nurse Practitioner'},
        {'id': 'PROV003', 'name': 'Dr. <PERSON>', 'type': 'Physician'}
    ]
    
    # Generate slots for 10 days
    for day in range(10):
        for provider in providers:
            for slot in range(np.random.randint(2, 5)):  # 2-4 slots per provider per day
                slot_time = base_date + timedelta(days=day, hours=8+slot*2)
                is_open = np.random.random() > 0.7  # 30% open slots
                
                slots.append({
                    'DEPARTMENT_ID': 999001,
                    'PROV_ID': provider['id'],
                    'PROV_NAME': provider['name'],
                    'PROV_TYPE': provider['type'],
                    'SLOT_BEGIN_TIME': slot_time,
                    'SLOT_BLOCKS': 'General',
                    'SLOT_LENGTH_MINUTES': 30,
                    'ORG_REG_OPENINGS': 1,
                    'NUM_APTS_SCHEDULED': 0 if is_open else 1,
                    'AVAILABLE_OPENINGS': 1 if is_open else 0
                })
    
    # Create appointments
    appointments = []
    for i in range(30):
        provider = np.random.choice(providers)
        # Mix of future and past appointments
        if i < 20:  # 20 future
            appt_time = base_date + timedelta(days=np.random.randint(1, 60))
        else:  # 10 past
            appt_time = base_date - timedelta(days=np.random.randint(1, 30))
        
        appointments.append({
            'PatientMrn': f"{100000000 + i:09d}",
            'DepartmentEpicId': 999001,
            'ProviderId': provider['id'],
            'ProviderName': provider['name'],
            'ProviderType': provider['type'],
            'AppointmentDateTime': appt_time,
            'VisitType': 'General Visit',
            'AppointmentLengthInMinutes': 30,
            'EncounterId': f"ENC{i:06d}",
            'AppointmentStatus': 'Scheduled'
        })
    
    return pd.DataFrame(slots), pd.DataFrame(appointments)

def analyze_data(slots_df, appointments_df):
    """Analyze the test data"""
    
    # Basic metrics
    total_slots = len(slots_df)
    open_slots = len(slots_df[slots_df['AVAILABLE_OPENINGS'] > 0])
    open_percentage = (open_slots / total_slots * 100) if total_slots > 0 else 0
    
    total_appointments = len(appointments_df)
    future_appointments = len(appointments_df[appointments_df['AppointmentDateTime'] > datetime.now()])
    
    # Provider utilization
    slots_df['Utilization'] = slots_df['NUM_APTS_SCHEDULED'] / slots_df['ORG_REG_OPENINGS']
    avg_utilization = slots_df['Utilization'].mean()
    
    # Wait times
    future_appts = appointments_df[appointments_df['AppointmentDateTime'] > datetime.now()]
    if len(future_appts) > 0:
        wait_days = (future_appts['AppointmentDateTime'] - datetime.now()).dt.days
        avg_wait_days = wait_days.mean()
    else:
        avg_wait_days = 0
    
    return {
        'total_slots': total_slots,
        'open_slots': open_slots,
        'open_percentage': open_percentage,
        'total_appointments': total_appointments,
        'future_appointments': future_appointments,
        'avg_utilization': avg_utilization,
        'avg_wait_days': avg_wait_days
    }

def create_charts(slots_df, appointments_df, results, output_dir):
    """Create analysis charts"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('OBD Test Results', fontsize=16, fontweight='bold')
    
    # Chart 1: Slot availability
    labels = ['Open', 'Booked']
    sizes = [results['open_slots'], results['total_slots'] - results['open_slots']]
    colors = ['#2ecc71', '#e74c3c']
    ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%')
    ax1.set_title('Slot Availability')
    
    # Chart 2: Provider utilization
    provider_util = slots_df.groupby('PROV_NAME')['Utilization'].mean()
    bars = ax2.bar(range(len(provider_util)), provider_util.values, 
                  color=['red' if u > 0.9 else 'orange' if u > 0.8 else 'green' 
                        for u in provider_util.values])
    ax2.set_title('Provider Utilization')
    ax2.set_ylabel('Utilization Rate')
    ax2.set_xticks(range(len(provider_util)))
    ax2.set_xticklabels([name.split()[-1] for name in provider_util.index])
    ax2.axhline(y=0.8, color='orange', linestyle='--', alpha=0.7)
    ax2.axhline(y=0.9, color='red', linestyle='--', alpha=0.7)
    
    # Chart 3: Key metrics
    metrics = ['Total Slots', 'Open Slots', 'Appointments', 'Future Appts']
    values = [results['total_slots'], results['open_slots'], 
             results['total_appointments'], results['future_appointments']]
    bars = ax3.bar(metrics, values, color=['#3498db', '#2ecc71', '#f39c12', '#9b59b6'])
    ax3.set_title('Key Metrics')
    ax3.set_ylabel('Count')
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
               str(value), ha='center', va='bottom', fontweight='bold')
    plt.setp(ax3.get_xticklabels(), rotation=45)
    
    # Chart 4: Wait time distribution
    future_appts = appointments_df[appointments_df['AppointmentDateTime'] > datetime.now()]
    if len(future_appts) > 0:
        wait_days = (future_appts['AppointmentDateTime'] - datetime.now()).dt.days
        ax4.hist(wait_days, bins=10, alpha=0.7, color='skyblue', edgecolor='black')
        ax4.axvline(wait_days.mean(), color='red', linestyle='--', linewidth=2,
                   label=f'Mean: {wait_days.mean():.1f} days')
        ax4.set_title('Wait Time Distribution')
        ax4.set_xlabel('Days')
        ax4.set_ylabel('Patients')
        ax4.legend()
    else:
        ax4.text(0.5, 0.5, 'No Future\nAppointments', ha='center', va='center',
                transform=ax4.transAxes, fontsize=12)
        ax4.set_title('Wait Time Distribution')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'obd_results.png', dpi=300, bbox_inches='tight')
    plt.close()

def save_results(slots_df, appointments_df, results, output_dir):
    """Save test data and results"""
    
    # Save data
    slots_df.to_csv(output_dir / 'test_slots.csv', index=False)
    appointments_df.to_csv(output_dir / 'test_appointments.csv', index=False)
    
    # Save report
    report = [
        "OBD Test Report",
        "=" * 20,
        f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "RESULTS:",
        f"  Total Slots: {results['total_slots']}",
        f"  Open Slots: {results['open_slots']} ({results['open_percentage']:.1f}%)",
        f"  Total Appointments: {results['total_appointments']}",
        f"  Future Appointments: {results['future_appointments']}",
        f"  Average Utilization: {results['avg_utilization']:.1%}",
        f"  Average Wait Days: {results['avg_wait_days']:.1f}",
        "",
        "FILES:",
        "  - obd_results.png (charts)",
        "  - test_slots.csv (slot data)",
        "  - test_appointments.csv (appointment data)",
        "  - report.txt (this report)"
    ]
    
    with open(output_dir / 'report.txt', 'w') as f:
        f.write('\n'.join(report))

def main():
    """Run the OBD test"""
    
    print("=" * 40)
    print("OBD TEST")
    print("=" * 40)
    
    # Setup
    output_dir = Path("obd_test_output")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # Step 1: Create test data
        print("\n1. Creating test data...")
        slots_df, appointments_df = create_test_data()
        print(f"   ✓ Created {len(slots_df)} slots and {len(appointments_df)} appointments")
        
        # Step 2: Analyze
        print("\n2. Analyzing data...")
        results = analyze_data(slots_df, appointments_df)
        
        # Step 3: Create charts
        print("\n3. Creating charts...")
        create_charts(slots_df, appointments_df, results, output_dir)
        print("   ✓ Created obd_results.png")
        
        # Step 4: Save results
        print("\n4. Saving results...")
        save_results(slots_df, appointments_df, results, output_dir)
        print("   ✓ Saved data and report")
        
        # Step 5: Show results
        print("\n" + "=" * 40)
        print("RESULTS")
        print("=" * 40)
        print(f"📊 Total Slots: {results['total_slots']}")
        print(f"🟢 Open Slots: {results['open_slots']} ({results['open_percentage']:.1f}%)")
        print(f"👥 Total Appointments: {results['total_appointments']}")
        print(f"📅 Future Appointments: {results['future_appointments']}")
        print(f"📈 Avg Utilization: {results['avg_utilization']:.1%}")
        print(f"⏰ Avg Wait Days: {results['avg_wait_days']:.1f}")
        
        print(f"\n📁 Files saved to: {output_dir}")
        print("✅ TEST COMPLETED")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
