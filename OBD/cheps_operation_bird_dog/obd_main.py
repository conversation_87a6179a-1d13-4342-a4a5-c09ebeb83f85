#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Mon Jun 23 10:35:24 2025

@author: armangetzen

This script is the driver file that runs functions to aid in Operation Bird Dog 
from both obd_init and obd_results. This will both generate lists of potential
slot/patient movement opportunities and report metrics on movement 
opporunities/open slots for each clinic.

"""

import obd_init
import obd_results
import os
import time
import pandas as pd


def main():
    # use current time to create unique output directory for organized OBD files
    now = time.strftime("%m_%d_%Y")
    
    # # list of OBD dept IDs
    dept_id_list = [109141002,
                    102101503,
                    102102003,
                    100021502,
                    100022101,
                    102281002,
                    102411001,
                    109232101,
                    109231501,
                    102101507,
                    100022001,
                    109112001,
                    102051007,
                    102051006,
                    109511004,
                    100021509,
                    100021501,
                    100021510,
                    109232102,
                    100020401]

    # list of OBD dept names (in same order as the IDs)
    dept_name_list = ["ecp1_orth_prost", "df_plastic", "df_ortho_sport", "tc_gen_surg_mis", "tc_uro", "nhc_orth_prost",
                      "bcsc_orth_prost", "cpb_uro", "cpb_gen_surg", "df_ortho_hand", "tc_ortho_surg", "hvp_ortho_surg",
                      "brl_pmr", "brl_pmr_spine", "prc_pmr_ped", "tc_endo", "tc_thoracic", "tc_gen_surg_colorectal",
                      "cpb_obgyn", "tc_neuro"]

    # loop through for each clinic
    for c, clinic in enumerate(dept_id_list):
        clinic_name = dept_name_list[c]
        prefix = f"{now}/{dept_name_list[c]}"
        #specify file path
        results_dir = f"/Volumes/cheps-research/OBD/OBD Daily Pulls/Processed Schedules/{prefix}"
        #specify input file_name
        input_filename = f"/Volumes/cheps-research/OBD/OBD Daily Pulls/cheps_operation_bird_dog/Clinic Input Configurations/{dept_name_list[c]}_input.txt"

        if not os.path.exists(results_dir):
            os.makedirs(results_dir)
            
        # Initialize an OBDScheduler instance for this department
        scheduler = obd_init.OBDScheduler()
        scheduler.initialize_input_vars(input_filename)

        slot_store_list, encounter_df, historical_slots, slot_horizon = scheduler.process_schedule(clinic, clinic_name)

        # output the csv of fillable slots and their MRNs to the correct directory
        output_file_slot = f"{results_dir}/{dept_name_list[c]}_slot_reschedule_opportunities.csv"
        slot_store_list.to_csv(output_file_slot, index=False)
        
        output_file_mrn = f"{results_dir}/{dept_name_list[c]}_mrn_list.csv"
        encounter_df.to_csv(output_file_mrn, index=False)
        
        output_file_all_slots = f"{results_dir}/{dept_name_list[c]}_all_slots_data.csv"
        historical_slots.to_csv(output_file_all_slots, index=False)
        
        # Run comprehensive analytics module
        results = obd_results.OBDResults()
        results.initialize_data_sources(results_dir, clinic_name)

        # Calculate OBD metrics
        obd_metrics = results.calculate_OBD_metrics(slot_horizon)

        # Analyze patient eligibility issues
        eligibility_stats = results.analyze_patient_eligibility_issues(
            scheduler.appt_data,
            scheduler.slot_data,
            scheduler.min_day_apart.days
        )

        # Store comprehensive reports
        report_file, summary_file = results.store_OBD_metrics(obd_metrics, eligibility_stats, clinic_name)

        print(f"Completed analysis for {clinic_name}")
        print(f"  - Slot opportunities: {output_file_slot}")
        print(f"  - Patient list: {output_file_mrn}")
        print(f"  - Analysis report: {report_file}")
        print(f"  - Summary report: {summary_file}")
        print(f"  - Found {obd_metrics['total_open_slots']} open slots with {obd_metrics['total_eligible_patients']} eligible patients")
        
        

if __name__ == '__main__':
    main()