import traceback
import logging
from simulations import ACCESS_v1
from bootstrap import ReadModule, InitModule, RunSimulationModule, GenSimMetricsModule, ReportModule
from simu_core.utils import log_errors

logger = logging.getLogger(__name__)

@log_errors
def run_simulation():
    sim_core = ACCESS_v1("data/file_locations_ACCESS_v1.txt")

    gen_params_file_key = "gen_params_file"
    cust_params_file_key = "cust_params_file"
    tool_params_file_key = "tool_params_file"
    output_file_key = "report_file"

    modules = [
        ReadModule(sim_core, gen_params_file_key, cust_params_file_key, tool_params_file_key), 
        InitModule(sim_core),
        RunSimulationModule(sim_core),
        GenSimMetricsModule(sim_core),
        ReportModule(sim_core, output_file_key)
    ]

    for module in modules:
        module.execute()

if __name__ == "__main__":
    run_simulation() 