from .custom_table import CustomTable
import logging
logger = logging.getLogger(__name__)


class MetricsTable(CustomTable):
    def __init__(self, df_type, columns_in):
        super().__init__(df_type, ['Replication'] + columns_in)
        self.Replication = 0

    def get_by_replication(self, rep_num):
        rep_num_sql = int(rep_num)
        sql = f"SELECT * FROM table WHERE Replication = {rep_num_sql}"
        return self.data_manager.read_query(sql)

    def increment_replication(self):
        self.Replication += 1

    def add_metric(self, metric):
        if not isinstance(metric, list) and not isinstance(metric, dict):
            raise TypeError("Metrics must be passed in as list or dictionary")
        if isinstance(metric, dict):
            metric = [metric]
        for e in metric:
            e["Replication"] = self.Replication

        self.add(metric)
