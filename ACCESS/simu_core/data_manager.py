from abc import ABC, abstractmethod
import logging
import warnings
import sys
import pandas as pd
import sqlite3
from pandasql import sqldf
from .exceptions import ColumnNotFoundError
warnings.filterwarnings('ignore', category=FutureWarning)
logger = logging.getLogger(__name__)


class DataManagerInterface(ABC):
    @abstractmethod
    def init_table(self, columns_in):
        pass

    @abstractmethod
    def filter(self, identifier, col_name):
        pass

    @abstractmethod
    def read_query(self, identifier, col_name):
        pass

    @abstractmethod
    def update_column(self, col_name, old_value, new_value, num_rows):
        pass

    @abstractmethod
    def update_rows(self, col_name, identifier, data):
        pass

    @abstractmethod
    def delete(self, col_name, identifier):
        pass

    @abstractmethod
    def access_manager(self):
        pass


class DataFrameDataManager(DataManagerInterface):
    """Implementation of DataManagerInterface using a pandas DataFrame.

    This class provides methods for initializing, querying, updating, 
    and managing tabular data in-memory using pandas. It supports 
    SQL-like queries, conditional updates, row and column modifications, 
    and DataFrame access and clearing operations.

    Attributes:
        _data_frame (pd.DataFrame): The DataFrame that stores the data.
    """
    _data_frame = pd.DataFrame()  # Shared DataFrame for all instances of this type
    # _instance = None  # Singleton instance

    # def __new__(cls, *args, **kwargs):
    #     if cls._instance is None:
    #         cls._instance = super().__new__(cls)
    #     return cls._instance

    def init_table(self, columns_in):
        """Initializes the DataFrame with user-specified columns.

        Args:
            columns_in (list(str)): A list of column names
        
        Returns:
            None
        """
        self.columns_in = columns_in
        self._data_frame = pd.DataFrame(columns=columns_in)
        return



    def filter(self, column, operator, value, cols_list ='*'):
        '''
        Filters rows that match a SQL-style condition and returns specified or all columns.

        Args:
            condition_statement (str): a SQL-compliant condition to 
                filter rows
            cols_list (str, optional): a string containing a list 
                of column names to return separated by commas, defaulted 
                to all columns.
        
        Returns:

          DataFrame: The filtered DataFrame
        '''        
        valid_ops = {"=", "<", ">", "<=", ">=", "<>", "IS", "IS NOT"}
        if operator not in valid_ops:
            raise ValueError(f"Invalid Operator: {operator}")
        
        if column not in self._data_frame.columns:
            raise ValueError(f"Invalid Column: {column}")
        
        if cols_list != '*':
            cols = [c.strip() for c in cols_list.split(',')]
            for c in cols:
                if c not in self._data_frame.columns:
                    raise ValueError(f"Invalid output column: {c!r}")
            cols_sql = ', '.join(f'"{c}"' for c in cols)
        else:
            cols_sql = '*'

        conn = sqlite3.connect(':memory:')
        self._data_frame.to_sql('df', conn, index=False)

        sql = f'SELECT {cols_sql} FROM df WHERE "{column}" {operator} ?'
        result = pd.read_sql_query(sql, conn, params=(value,))

        conn.close()
        return result
        
        
    def read_query(self, query_statement):
        """Performs a user-given read-only SQL query on the DataFrame.

        Args:
            query_statement (str): A string containing the SQL query
        
        Returns:
            DataFrame: a temporary DataFrame view of the query result
        """
        statement = query_statement.split(" ")
        df_idx = statement.index("FROM") + 1
        statement[df_idx] = 'df'
        query = " ".join(statement)
        return sqldf(query, env={'df': self._data_frame})

    def update_column(self, col_name, old_value, new_value, num_rows=-1):
        """
        Selects a set number of rows that have a user-set column target 
        value. Updates selected rows to have a new value in the same 
        column.
        
        Args:
            col_name (str): The column to check for a target value
            old_value (any): The target value
            new_value (any): The new value to assign to selected rows
            num_rows (int, optional): The number of rows to update 
                (in order); defaulted to -1 for all rows
        
        Returns:
            None
        """
        if (num_rows == -1):
            if old_value is None:
                self._data_frame.loc[pd.isna(self._data_frame[col_name]), col_name] = new_value
            else:
                self._data_frame.loc[self._data_frame[col_name] == old_value, col_name] = new_value
        else:    # Only a select number of num_rows should be updated
            if old_value is None:
                rows_to_update = self._data_frame.loc[
                    pd.isna(self._data_frame[col_name]), col_name
                ].head(num_rows).index
                self._data_frame.loc[rows_to_update, col_name] = new_value
            else:
                rows_to_update = self._data_frame.loc[
                    self._data_frame[col_name] == old_value, col_name
                ].head(num_rows).index
                self._data_frame.loc[rows_to_update, col_name] = new_value

    def conditional_update_column(self, condition_col, condition_value, update_col, new_value):
        """
        Selects all rows that have a user-set column target value.
        Updates selected rows to have a new value in another user-set 
        column.
        
        Args:
            condition_col (str): The column to check for a target value
            condition_value (any): The target value
            update_col (str): The column to update in selected rows
            new_value (any): The new value for selected rows' update_col 
                values
        
        Returns:
            None
        """
        if condition_value is None:
            self._data_frame.loc[pd.isna(self._data_frame[condition_col]), update_col] = new_value
        else:
            self._data_frame.loc[
                self._data_frame[condition_col] == condition_value, update_col
            ] = new_value

    # update should update all columns with a prior_value to be
    # instantiated with an entire new row of data

    def update_rows(self, col_name, identifier, data):
        """
        Selects rows that match a specified target colum value (defaulted to ID) and 
        replaces them with a new row given by the user.
        
        Args:
            col_name (str): The name of the column to check for a 
                target ID
            identifier (any): The target value
            data (list(any)): The new column values to replace selected 
                rows.
            
        Returns:
            None
        """
        if col_name in self._data_frame.columns:
            idx = self._data_frame[self._data_frame[col_name] == identifier].index
            if not idx.empty:
                self._data_frame.loc[idx, list(
                    data.keys())] = list(data.values())
                return self._data_frame.loc[idx]
        return None

    def delete(self, col_name, identifier):
        """
        Deletes rows where a specified column matches a target value
        
        Args:
            col_name (str): The name of the column to check
            identifier (any): The target value
            
        Returns:
            DataFrame: The modified DataFrame (itself)

        Raises:
            ColumnNotFoundError: If the specified column does not exist 
                in the DataFrame.
        """
        if col_name in self._data_frame.columns:
            self._data_frame = self._data_frame[self._data_frame[col_name] != identifier]
        else:
            raise ColumnNotFoundError(f"Column name: {col_name} not contained in dataframe")
        return self._data_frame

    def add(self, values):
        """Adds one or more rows with user-given column values.
        
        Args:
            values(dict or list of dict): A single dictionary of one 
                row, or a list of dictionaries of multiple rows. Each 
                dictionary maps column names to their values.
        
        Returns:
            None

        Raises:
            ColumnNotFoundError: If any of the columns in the inserted 
                row(s) do not exist in the DataFrame.
            ValueError: If the input is neither a dict nor a list of 
                dicts.
        """
        df_cols = set(self._data_frame.columns)

        if isinstance(values, list):
            for row in values:
                row_cols = set(row.keys())
                extra = row_cols - df_cols
                if extra:  # There are extra columns in inserted row
                    raise ColumnNotFoundError(
                        f"The row {row} has nonexistent {len(extra)} columns {extra}.")
            self._data_frame = pd.concat(
                [self._data_frame, pd.DataFrame(values)],
                ignore_index=True)
        elif isinstance(values, dict):
            row_cols = set(values.keys())
            extra = row_cols - df_cols
            if extra:  # There are extra columns in inserted row
                raise ColumnNotFoundError(
                    f"The row {values} has nonexistent {len(extra)} columns {extra}.")
            self._data_frame = pd.concat(
                [self._data_frame, pd.DataFrame([values])],
                ignore_index=True)
        else:
            raise ValueError("The given row must be a dict or a list of dicts")

    def access_manager(self):
        """Returns the DataFrame.(itself)
        
        Args:
            None
        
        Returns:
            DataFrame
        """
        return self._data_frame

    def clear(self):
        """Clears all data from the the DataFrame.

        Args:
            None
        
        Returns:
            None
        """
        self._data_frame = pd.DataFrame(columns=self.columns_in)


# class SQLManager(DataManagerInterface):
#     _instance = None  # Singleton instance
#     _data_frame = pd.DataFrame().to_sql()


class DataFrameFactory:
    def init_df(df_type):
        if df_type == "dataframe":
            logger.info("Initializing DataFrame")
            return DataFrameDataManager()
        # if type == 'database':
        #     logger.info("Initializing SQL")
        #     return SQLManager()
        print("Unkwown data manager interface")
        sys.exit(1)
