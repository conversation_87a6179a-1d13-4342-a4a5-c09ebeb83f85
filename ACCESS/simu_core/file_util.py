from pathlib import Path

class FileUtil():
    def __init__(self, file_locations_filename):
        self.file_locations_filename = file_locations_filename
        self.file_locations_map = {}
        self.init_file_info_map()

    def init_file_info_map(self):
        try:
            with open(self.file_locations_filename, 'r', encoding='utf-8') as file:
                for line in file:
                    line = line.strip()
                    line = line.split()
                    filename = line[0]
                    filepath = line[1]
                    self.file_locations_map[filename] = filepath
        except FileNotFoundError as e:
            raise FileNotFoundError(
                f"Error: The file '{self.file_locations_filename}' does not exist.") from e

    def get_file(self, file_key: str):
        if file_key not in self.file_locations_map:
            raise KeyError(f"Error: {file_key} was not in file_info.txt")

        file = Path(self.file_locations_map[file_key])
        if file.exists():
            return self.file_locations_map[file_key]

        raise FileNotFoundError(f"Error: {file} does not exist")
