import logging
from .data_manager import DataFrameFactory
logger = logging.getLogger(__name__)


class CustomTable():
    def __init__(self, df_type, columns_in):
        self.data_manager = DataFrameFactory.init_df(df_type)
        self.data_manager.init_table(["ID"] + columns_in)
        self.ID = 0

    def get_all(self):
        return self.data_manager.read_query('SELECT * FROM table WHERE 1=1')

    def get_by_id(self, id):
        id_sql = int(id)
        sql = f"SELECT * FROM table WHERE ID = {id_sql}"
        return self.data_manager.read_query(sql)

    def get_by_column_value(self, column, value):
        if column not in self.data_manager.access_manager().columns:
            raise ValueError(f"{column!r} is not a valid column")
        if value is None or (isinstance(value, str) and value.upper() == "NULL"):
            return self.data_manager.filter(
                column   = column,
                operator = "IS",
                value    = None,
            )
        else:
            return self.data_manager.filter(
                column   = column,
                operator = "=",
                value    = value
            )

    def add(self, item):
        if isinstance(item, dict):
            item = [item]
        if not isinstance(item, list):
            raise TypeError("Items must be passed in as list of dictionaries")
        for e in item:
            e["ID"] = self.ID
            self.ID += 1
            self.data_manager.add(e)

    def update_column_values(
            self, col_name, old_value, new_value, num_rows=-1):
        return self.data_manager.update_column(
            col_name, old_value, new_value, num_rows)

    def conditional_update_column_values(
            self, condition_col, condition_value, update_col, new_value):
        return self.data_manager.conditional_update_column(
            condition_col, condition_value, update_col, new_value)

    def read_query(self, statement):
        return self.data_manager.read_query(statement)
    