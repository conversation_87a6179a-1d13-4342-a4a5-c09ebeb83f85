import logging
from enum import Enum
from .table import Table

logger = logging.getLogger(__name__)

class EventTable(Table):
    def __init__(self, df_type, columns_in, eventType: Enum):
        # Enforce that there is an event type column
        if 'eventType' not in columns_in:
            columns_in = ['eventType'] + columns_in
        
        # Enforce that eventType is an enum
        if not isinstance(eventType, Enum) and not issubclass(eventType, Enum):
            raise TypeError("Event type must be an enum class")

        super().__init__(df_type, columns_in)
        self.eventType = eventType
    
    def add_event(self, event):
        if not isinstance(event, list) and not isinstance(event, dict):
            raise TypeError("Events must be passed in as list or dictionary")
        if isinstance(event, dict):
            event = [event]
        for e in event:
            if not isinstance(e["eventType"], self.eventType):
                raise TypeError(
                    f"eventType column must contain only values of type {self.eventType.__name__}")
            e["eventType"] = e["eventType"].value
        
        self.add(event)

    def clear(self):
        self.data_manager.clear()