from abc import ABC, abstractmethod
import logging
from enum import Enum
logger = logging.getLogger(__name__)

class SimuCore(ABC):
    """Abstract base class for core simulation functionality.
    
    This class defines the methods for a simulation framework, 
    including event and task management, input handling, and
    report generation. It is designed to be subclassed for specific
    simulation implementations.

    Attributes:
        persistent_data (dict): A dictionary to store persistent data 
            across simulation runs.
    """

    def __init__(self, eventType: Enum, taskType: Enum):
        """Initialize an instance of SimuCore with event and task types.
        
        Args:
            eventType (Enum)
            taskType (Enum)

        Raises:
            TypeError: If eventType or taskType is not a subclass of 
                Enum.
        """
        # self.event_table = pd.DataFrame()
        self.persistent_data = {}
        logging.basicConfig(
            filename="data/output/debug/log.txt",
            format='%(asctime)s %(levelname)s: %(name)s - %(message)s',
            datefmt='%H:%M:%S')
        logging.getLogger().setLevel(logging.WARNING)
        logging.getLogger('simulations').setLevel(logging.DEBUG)
        logging.getLogger('bootstrap').setLevel(logging.DEBUG)

        if not isinstance(eventType, type) or not issubclass(eventType, Enum):
            raise TypeError(f"{eventType} must be a subclass of Enum")
        
        if not isinstance(taskType, type) or not issubclass(taskType, Enum):
            raise TypeError(f"{taskType} must be a subclass of Enum")

    @abstractmethod
    def read_input(self, gen_params_dict, cust_params_dict, tool_params_dict):
        pass

    @abstractmethod
    def validate_input(self):
        pass

    def custom_simulation_stopping_criteria_met(self):
        raise RuntimeError("Custom simulation stopping criteria selected but not implemented")
    
    @abstractmethod
    def condition_true(self):
        pass

    @abstractmethod
    def generate_reports(self, key):
        pass

    @abstractmethod
    def gen_events(self):
        pass

    @abstractmethod
    def gen_tasks(self, replication):
        pass

    @abstractmethod
    def do_tasks(self):
        pass

    @abstractmethod
    def reset(self):
        pass

    @abstractmethod
    def calculate_statistics(self):
        pass

    @abstractmethod
    def generate_replication_metrics(self):
        pass
