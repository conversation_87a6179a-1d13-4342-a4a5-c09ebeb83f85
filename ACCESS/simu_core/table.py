from .data_manager import DataFrameFactory
import logging
from datetime import datetime
logger = logging.getLogger(__name__)
from pandasql import sqldf


class Table():
    def __init__(self, df_type, columns_in):
        self.data_manager = DataFrameFactory.init_df(df_type)
        self.data_manager.init_table(
            ['ID', 'Replication', 'interval', 'created_at', 'updated_at'] +
            columns_in)
        self.ID = 0
        self.Replication = 0
        self.interval = 0

    def get_all(self):
        return self.data_manager.read_query('SELECT * FROM table WHERE 1=1')

    def get_by_id(self, id):
        id_sql = int(id)
        sql = f"SELECT * FROM table WHERE ID = {id_sql}"
        return self.data_manager.read_query(sql)

    def get_by_interval(self, interval):
        try:
            interval_val = int(interval)
            replication_val = int(self.Replication)
        except (TypeError, ValueError):
            raise ValueError(f"Interval and Replication must be integers")

        sql = f"SELECT * FROM table WHERE interval = {interval_val} and Replication = {replication_val}"
        return self.data_manager.read_query(sql)

    def get_by_replication(self, rep_num):
        rep_num_sql = int(rep_num)
        sql = f"SELECT * FROM table WHERE Replication = {rep_num_sql}"
        return self.data_manager.read_query(sql)

    def get_by_column_value(self, column, value):
        if column not in self.data_manager.access_manager().columns:
            raise ValueError(f"{column!r} is not a valid column")
        if value is None or (isinstance(value, str) and value.upper() == "NULL"):
            return self.data_manager.filter(
                column   = column,
                operator = "IS",
                value    = None,
            )
        else:
            return self.data_manager.filter(
                column   = column,
                operator = "=",
                value    = value
            )

    def add(self, item, wait_for_n_interval=0):
        if isinstance(item, dict):
            item = [item]
        if not isinstance(item, list):
            raise TypeError("Items must be passed in as a dict or a list of dicts")
        for e in item:
            e["ID"] = self.ID
            e["interval"] = self.interval + wait_for_n_interval
            e["Replication"] = self.Replication
            e['created_at'] = datetime.now()
            e['updated_at'] = datetime.now()
            self.ID += 1
            self.data_manager.add(e)

    def update_column_values(
            self, col_name, old_value, new_value, num_rows=-1):
        return self.data_manager.update_column(
            col_name, old_value, new_value, num_rows)

    def read_query(self, statement):
        return self.data_manager.read_query(statement)

    def increment_interval(self):
        self.interval += 1

    def increment_replication(self):
        self.Replication += 1
        self.interval = 0
