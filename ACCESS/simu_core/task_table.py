from enum import Enum
import logging
from .table import Table

logger = logging.getLogger(__name__)


class TaskTable(Table):
    def __init__(self, df_type, columns_in, taskType: Enum):
        if 'event_id' not in columns_in:
            columns_in.insert(0, 'event_id')

        # Enforce that there is an event type column
        if 'taskType' not in columns_in:
            columns_in = ['taskType'] + columns_in

        # Enforce that eventType is an enum
        if not isinstance(taskType, Enum) and not issubclass(taskType, Enum):
            raise TypeError("Task type must be an enum class")

        super().__init__(df_type, columns_in)
        self.taskType = taskType

    def add_task(self, tasks, wait_for_n_interval=0):
        if isinstance(tasks, dict):
            tasks = [tasks]
        if not isinstance(tasks, list):
            raise TypeError("Tasks must be passed in as list of dicts")
        for t in tasks:
            # e = tasks[i]
            if not isinstance(t["taskType"], self.taskType):
                raise TypeError(
                    f"taskType column must contain only values of type {self.taskType.__name__}")
            t["taskType"] = t["taskType"].value

        self.add(tasks, wait_for_n_interval)

    def clear(self):
        self.data_manager.clear()
