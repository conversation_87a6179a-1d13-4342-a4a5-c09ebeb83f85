import pytest
import pandas as pd
from simu_core.custom_table import CustomTable
from simu_core.exceptions import ColumnNotFoundError

@pytest.fixture
def custom_table():
    """Create an instance of CustomTable."""
    table = CustomTable('dataframe', ['name', 'value'])
    yield table


def test_init_custom_table(custom_table):
    """Verify correct initialization of an empty CustomTable."""
    df = custom_table.get_all()
    assert (
        isinstance(df, pd.DataFrame)
    ), f"Expected df to be a pandas DataFrame, got {type(df)}"
    assert df.empty, f"Expected df to be empty, got {len(df)} rows"


def test_add_empty_list(custom_table):
    """Attempt to add empty list and verify it adds no rows to custom table."""
    custom_table.add([])
    df = custom_table.get_all()
    assert len(df) == 0


def test_add_single_dict(custom_table):
    """Add a single row as a dict and verify it is added correctly."""
    row = {"name": "accuracy", "value": 0.95}
    custom_table.add([row])
    df = custom_table.get_all()
    assert len(df) == 1
    assert df.iloc[0]["name"] == "accuracy"
    assert df.iloc[0]["value"] == 0.95


def test_add_list_of_dicts(custom_table):
    """Add multiple rows as a list of dicts and verify they are added correctly."""
    rows = [
        {"name": "precision", "value": 0.9},
        {"name": "recall", "value": 0.85}
    ]
    custom_table.add(rows)
    df = custom_table.get_all()
    assert len(df) == 2
    assert set(df["name"]) == {"precision", "recall"}


def test_add_list_of_tuples_raises_error(custom_table):
    """Attempt to add a list of tuples and verify it raises an Exception."""
    with pytest.raises(TypeError):
        custom_table.add([("name", "tuple1"), ("name", "tuple2")])


def test_add_list_of_lists_raises_error(custom_table):
    """Attempt to add a list of lists and verify it raises an Exception."""
    with pytest.raises(TypeError):
        custom_table.add([["name", "list1"], ["name", "list2"]])


def test_add_missing_column(custom_table):
    """Add a row with missing columns and verify that it adds NaN for missing columns."""
    custom_table.add([{"name": "partial"}])
    df = custom_table.get_all()
    assert pd.isna(df.iloc[0]["value"])


def test_add_extra_column(custom_table):
    """
    Add a row with extra columns and verify that it ignores
    rows with extra columns.
    """
    with pytest.raises(ColumnNotFoundError):
        custom_table.add([{"name": "extra", "value": 1.2, "extra_col": 123}])

    df = custom_table.get_all()
    assert len(df) == 0, "Rows with extra columns should not be added to table."
    assert "extra_col" not in df.columns, "Table should not contain any extra columns."


def test_add_invalid_type(custom_table):
    """Test add with invalid type raises error."""
    with pytest.raises(TypeError):
        custom_table.add("not a dict or list")


def test_get_by_id(custom_table):
    """Test getting a row by ID."""
    custom_table.add([{"name": "auc", "value": 0.99}])
    df = custom_table.get_all()
    row_id = df.iloc[0]["ID"]
    row = custom_table.get_by_id(row_id)
    assert not row.empty
    assert row.iloc[0]["name"] == "auc"


def test_get_by_id_invalid(custom_table):
    """Test get_by_id with an invalid ID returns empty DataFrame."""
    df = custom_table.get_by_id(-1)
    assert df.empty


def test_get_by_column_value_no_match(custom_table):
    """Test get_by_column_value returns empty DataFrame if no match."""
    custom_table.add({"name": "metric1", "value": 1.0})
    df = custom_table.get_by_column_value("name", "not_present")
    assert df.empty


def test_get_by_column_value(custom_table):
    """Test filtering by column value."""
    custom_table.add([{"name": "metric1", "value": 1.0}])
    custom_table.add([{"name": "metric2", "value": 2.0}])
    df = custom_table.get_by_column_value("name", "metric2")
    assert len(df) == 1
    assert df.iloc[0]["name"] == "metric2"


def test_update_column_values_no_match(custom_table):
    """Test update_column_values where no rows match the old value."""
    custom_table.add([{"name": "loss", "value": 0.5}])
    custom_table.update_column_values("value", 999, 0.4)
    df = custom_table.get_all()
    assert df.iloc[0]["value"] == 0.5


def test_conditional_update_column_values_no_match(custom_table):
    """Test conditional_update_column_values where no rows match the condition."""
    custom_table.add([{"name": "acc", "value": 0.7}])
    custom_table.conditional_update_column_values("name", "'not_acc'", "value", 0.8)
    df = custom_table.get_all()
    assert df.iloc[0]["value"] == 0.7


def test_conditional_update_column_values(custom_table):
    """Test conditional update of column values."""
    custom_table.add([{"name": "acc", "value": 0.7}])
    custom_table.conditional_update_column_values("name", "acc", "value", 0.8)
    df = custom_table.get_all()
    assert df.iloc[0]["value"] == 0.8
