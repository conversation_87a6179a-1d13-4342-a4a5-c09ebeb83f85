from enum import Enum
import pytest
import pandas as pd
from pandasql import PandaSQLException
from simu_core.event_table import EventTable

class eventType(Enum):
    A = 0
    B = 1

@pytest.fixture
def event_table():
    """Create an instance of EventTable."""
    event_table = EventTable('dataframe',['Value'], eventType= eventType)
    yield event_table
    event_table.clear() 


def test_init_event_table(event_table):
    """Test correct initialization of an empty EventTable."""
    df = event_table.get_all()
    assert isinstance(df, pd.DataFrame), f"Expected df to be a pandas DataFrame, got {type(df)}"
    assert df.empty, f"Expected df to be empty, got {len(df)} rows"
    assert event_table.ID == 0, f"Expected event_table.ID to be 0, got {event_table.ID}"
    assert event_table.interval == 0, (
        f"Expected event_table.interval to be 0, got {event_table.interval}")
    assert event_table.Replication == 0, (
        f"Expected event_table.Replication to be 0, got {event_table.Replication}")


def test_add_single_event(event_table):
    """
    Add a single event to the event table 
    and verify that the event table contains the event.
    """
    single_event = {'eventType': eventType.A, 'Value': 0}
    event_table.add_event([single_event])
    assert (
        len(event_table.get_all()) == 1
    ), f"Expected 1 event, got {len(event_table.get_all())}"
    

def test_add_multiple_events(event_table):
    """
    Add two events to the event table and verify that both are present.
    """
    events = [
        {'eventType': eventType.A, 'Value': 10},
        {'eventType': eventType.B, 'Value': 20}
    ]
    event_table.add_event(events)
    df = event_table.get_all()
    assert len(df) == 2, f"Expected 2 events, got {len(df)}"
    assert set(df['Value']) == {10, 20}, f"Expected Values 10 and 20, got {set(df['Value'])}"
    assert (
        set(df['eventType']) == {eventType.A.value, eventType.B.value}
    ), f"Expected eventTypes {eventType.A.value} and {eventType.B.value}, " \
        "got {set(df['eventType'])}"


def test_get_by_id(event_table):
    """Retrieve events by ID and verify their values."""
    event0 = {'ID': 0, "eventType":eventType.A, 'Value':0}
    event1 = {'ID': 1, "eventType":eventType.B, 'Value':1}
    event2 = {'ID': 2, "eventType":eventType.A, 'Value':2}
    events = [event0, event1, event2]
    event_table.add_event(events)

    df0 = event_table.get_by_id(0)
    assert len(df0) == 1, f"Expected 1 event with ID 0, got {len(df0)}"
    assert df0.iloc[0]['Value'] == 0, f"Expected Value 0 for ID 0, got {df0.iloc[0]['Value']}"

    df1 = event_table.get_by_id(1)
    assert len(df1) == 1, f"Expected 1 event with ID 1, got {len(df1)}"
    assert df1.iloc[0]['Value'] == 1, f"Expected Value 1 for ID 1, got {df1.iloc[0]['Value']}"

    df2 = event_table.get_by_id(2)
    assert len(df2) == 1, f"Expected 1 event with ID 2, got {len(df2)}".format(len(df2))
    assert df2.iloc[0]['Value'] == 2, f"Expected Value 2 for ID 2, got {df2.iloc[0]['Value']}"


def test_get_nonexistent_col_value(event_table):
    """
    Test that get_by_column_value returns an empty DataFrame 
    when filtering by a non-existent column name and value.
    """
    ev = {'eventType': eventType.A, 'Value': None}
    event_table.add_event(ev)

    with pytest.raises(PandaSQLException):
        df_null = event_table.get_by_column_value('column', 'NULL')
        assert len(df_null) == 0, f"Expected 0 event  got {len(df_null)}"
        assert (
            pd.isna(df_null.iloc[0]['Value'])
        ), f"Expected Value to be NaN, got {df_null.iloc[0]['Value']}"


def test_get_nonexistent_id_or_interval(event_table):
    """
    Test that get_by_id, get_by_interval, and get_by_replication return empty DataFrames
    when filtering by a non-existent ID, interval, or replication number.
    """
    assert event_table.get_by_id(0).empty
    assert event_table.get_by_interval(0).empty
    assert event_table.get_by_replication(0).empty

    events = [
        {'eventType':eventType.A, 'Value': 10},
        {'eventType':eventType.B, 'Value': 20}
    ]

    event_table.add_event(events)
    
    assert event_table.get_by_interval(99).empty
    assert event_table.get_by_id(99).empty
    assert event_table.get_by_replication(99).empty


def test_event_not_passed_correct(event_table):
    """Test that an error is raised when an event is not passed in correctly."""
    with pytest.raises(TypeError):
        event_table.add_event(123)
        
    incorrect_event = {'eventType':"incorrect", 'Value':1}
    with pytest.raises(TypeError):
        event_table.add_event(incorrect_event)

    with pytest.raises(TypeError):
        event_table.add_event([incorrect_event])


def test_update_column_values(event_table):
    """Tests the update column values function and updating nonexistent columns."""
    events = [
        {'eventType': eventType.A, 'Value': 10},
        {'eventType': eventType.B, 'Value': 30}, 
        {'eventType': eventType.A, 'Value': 10},
        {'eventType': eventType.A, 'Value': 70}, 
        {'eventType': eventType.B, 'Value': 30},
        {'eventType': eventType.B, 'Value': 10},  
    ]

    event_table.add_event(events)

    res = event_table.update_column_values('Value', 10, 15)
    assert res is None

    df = event_table.get_all()

    assert len(df[df['Value'] == 15]) == 3
    assert len(df[df['Value'] == 70]) == 1

    event_table.update_column_values('Value', 30, 99, num_rows=1)
    df2 = event_table.get_all()
    assert len(df2[df2['Value'] == 99]) == 1
    assert len(df2[df2['Value'] == 30]) == 1

    df0 = event_table.update_column_values('Value', 999, 123)
    assert df0 is None


def test_replication_interval_resets(event_table):
    """tests that interval will correctly reset after incrementing replication"""
    event_table.increment_interval()
    event_table.increment_replication()

    assert event_table.interval == 0
    assert event_table.Replication == 1

    event = {'eventType':eventType.A, 'Value':10}
    event_table.add_event(event)
    df = event_table.get_all()

    assert df.iloc[0]['Replication'] == 1
    assert df.iloc[0]['interval'] == 0


def test_interval_new_events(event_table):
    """
    Tests that added events will carry the correct interval number after incrementing interval.
    """
    event_table.increment_interval()
    event_table.increment_interval()

    event = {'eventType':eventType.A, 'Value':5}
    event_table.add_event(event)
    df = event_table.get_all()

    assert len(df) == 1
    assert df.iloc[0]['interval'] == 2


def test_add_missing_event(event_table):
    """throws error for trying to add event without eventType"""
    with pytest.raises(KeyError):
        event_table.add_event({'Value':1})


# def test_read_query(event_table):
#     df = event_table.read_query("SELECT 1")
#     assert isinstance(df, pd.Dataframe)