import pytest
import pandas as pd
import time
import logging
from simu_core.file_util import FileUtil
logger = logging.getLogger(__name__)


def test_single_file():
    """
    Test the initialization and the key/value pairs for a file_info file that
    contains exactly one file path
    """
    file = FileUtil("tests/test_file_util_input/test_single_file.txt")
    assert file.get_file("gen_params_file") == "data/input/coin_toss/gen_params.yaml"


def test_mult_file():
    """
    Test the initialization and the key/value pairs for a file_info file that
    contains more than one file path
    """
    file = FileUtil("tests/test_file_util_input/test_mult_file.txt")
    assert file.get_file("gen_params_file") == "data/input/coin_toss/gen_params.yaml"
    assert file.get_file("cust_params_file") == "data/input/coin_toss/gen_params.yaml"


def test_no_files():
    """
    Test the initialization if file_info contains no file paths
    """
    file = FileUtil("tests/test_file_util_input/test_no_files.txt")
    