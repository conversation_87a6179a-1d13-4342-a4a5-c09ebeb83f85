import pytest
import pandas as pd
from simu_core.metrics_table import MetricsTable

@pytest.fixture
def metrics_table():
    """Create an instance of MetricsTable."""
    metrics_table = MetricsTable('dataframe', ['name', 'value'])
    yield metrics_table


def test_init_metrics_table(metrics_table):
    """Test correct initialization of an empty MetricsTable."""
    df = metrics_table.get_all()
    assert isinstance(df, pd.DataFrame), f"Expected df to be a pandas DataFrame, got {type(df)}"
    assert df.empty, f"Expected df to be empty, got {len(df)} rows"
    assert metrics_table.Replication == 0, (
        f"Expected metrics_table.Replication to be 0, got {metrics_table.Replication}")


def test_add_metric_single_dict(metrics_table):
    """Add a single metric as a dict and verify it is added correctly."""
    metric = {"name": "accuracy", "value": 0.95}
    # TODO: modify MetricsTable to accept single dicts
    metrics_table.add_metric(metric)
    df = metrics_table.get_all()
    assert len(df) == 1, f"Expected 1 row, got {len(df)}"
    assert df.iloc[0]["name"] == "accuracy"
    assert df.iloc[0]["value"] == 0.95
    assert df.iloc[0]["Replication"] == 0


def test_add_metric_multiple_replications(metrics_table):
    """Test add_metric with multiple replications and verify Replication column."""
    metrics_table.add_metric([{"name": "m1", "value": 1}])
    metrics_table.increment_replication()
    metrics_table.add_metric([{"name": "m2", "value": 2}])
    df = metrics_table.get_all()
    assert set(df["Replication"]) == {0, 1}


def test_increment_replication(metrics_table):
    """Test incrementing the replication counter."""
    assert metrics_table.Replication == 0
    metrics_table.increment_replication()
    assert metrics_table.Replication == 1


def test_get_by_replication(metrics_table):
    """Test filtering by replication number."""
    metrics_table.add_metric([{"name": "f1", "value": 0.8}])
    metrics_table.increment_replication()
    metrics_table.add_metric([{"name": "f1", "value": 0.82}])
    df_rep0 = metrics_table.get_by_replication(0)
    df_rep1 = metrics_table.get_by_replication(1)
    assert len(df_rep0) == 1
    assert len(df_rep1) == 1
    assert df_rep0.iloc[0]["Replication"] == 0
    assert df_rep1.iloc[0]["Replication"] == 1


def test_get_by_replication_no_rows(metrics_table):
    """Test get_by_replication returns empty DataFrame if no rows for that replication."""
    df = metrics_table.get_by_replication(99)
    assert df.empty