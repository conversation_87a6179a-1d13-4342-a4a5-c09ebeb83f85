import time
import logging
import pytest
import pandas as pd
from simu_core.data_manager import DataFrameDataManager
from simu_core.exceptions import ColumnNotFoundError
logger = logging.getLogger(__name__)

@pytest.fixture
def df_manager():
    """Create a dataframe with specified columns using DataFrameDataManager."""
    df_manager = DataFrameDataManager()
    df_manager.init_table(['ID', 'Name', 'Value', 'Timestamp'])
    yield df_manager
    df_manager.clear()


def test_init_df_manager(df_manager):
    """
    Verify that the DataFrameDataManager initializes a dataframe 
    with the correct columns in any order.
    """
    df = df_manager.access_manager()
    assert isinstance(df, pd.DataFrame), "access_manager() did not return a DataFrame."

    columns_actual = set(df.columns)
    columns_expected = set(['ID', 'Name', 'Value', 'Timestamp'])
    assert(
        columns_actual == columns_expected
    ), "DataFrame columns do not match expected columns."
    

def test_add_empty(df_manager):
    """
    Add an empty list/dict to the DataFrame 
    and verify that it adds a row with NaN values for all columns.
    """
    df_manager.add([{}, {}, {}])
    df_manager.add({})
    df = df_manager.access_manager()

    # DataFrame should contain row(s) with NaN column values
    assert not df.empty, "DataFrame is empty after adding an empty list."


def test_add_single_and_multiple_rows(df_manager):
    """
    Add a single row and multiple rows to the DataFrame 
    and verify that they are added correctly.
    """
    expected_time = time.time()
    new_rows = [
        {'ID': 1, 'Name': "John", 'Value': 26, 'Timestamp': expected_time}, 
        {'ID': 2, 'Name': "Indiana", 'Value': 25, 'Timestamp': expected_time}, 
        {'ID': 3, 'Name': "Jerry", 'Value': 26, 'Timestamp': expected_time}
    ]
    new_row = {'ID': 4, 'Name': "Sarah", 'Value': 27, 'Timestamp': time.time()}

    df_manager.add(new_rows)
    df_manager.add(new_row)
    
    expected_rows = new_rows + [new_row]
    actual_rows = df_manager.access_manager().to_dict(orient='records')
    assert expected_rows == actual_rows


def test_add_extra_column(df_manager):
    """"
    Attempt to add a single row with one extra column than specified in 
    DataFrame initialization and verify that it is not added to the 
    DataFrame.
    """
    with pytest.raises(ColumnNotFoundError):
        df_manager.add({'ID': 5, 'Name': "Alice", 'Value': 30, 'ExtraColumn': "Extra"})
        
    df = df_manager.access_manager()
    assert df.empty, "DataFrame should not contain rows with extra columns."
    assert 'ExtraColumn' not in df.columns, "Extra column should not be present in DataFrame."

    
def test_add_missing_columns(df_manager):
    """
    Add a dictionary with some missing columns 
    and verify that missing columns are filled with NaN.
    """
    df_manager.add({'ID': 10})
    df = df_manager.access_manager()

    assert not df.empty, "DataFrame is empty after adding incomplete but valid data."
    assert (
        df.iloc[-1].drop('ID').isna().all()
    ), "All columns except ID should be NaN for incomplete data."
    

def test_delete_single_row(df_manager):
    """
    Add rows to the DataFrame and delete a row by identifier.
    Verify that the row is removed and the DataFrame is updated correctly.
    """
    df_manager.add([
        {'ID': 1, 'Name': "John", 'Value': 26, 'Timestamp': time.time()},
        {'ID': 2, 'Name': "Indiana", 'Value': 25, 'Timestamp': time.time()},
        {'ID': 3, 'Name': "Jerry", 'Value': 26, 'Timestamp': time.time()}
    ])
    
    df_manager.delete('ID', 2)  
    
    df = df_manager.access_manager()
    assert len(df) == 2, "DataFrame should have 2 rows after deletion."
    assert not (df['ID'] == 2).any(), "Row with ID 2 should be deleted."


def test_delete_nonexistent_row(df_manager):
    """
    Attempt to delete a row that does not exist 
    and verify that the DataFrame remains unchanged.
    """
    df_manager.add([
        {'ID': 1, 'Name': "John", 'Value': 26, 'Timestamp': time.time()},
        {'ID': 2, 'Name': "Indiana", 'Value': 25, 'Timestamp': time.time()}
    ])
    
    initial_length = len(df_manager.access_manager())
    df_manager.delete('ID', 3)  # Attempt to delete non-existent ID
    
    new_length = len(df_manager.access_manager())
    assert (
        new_length == initial_length
    ), "DataFrame length should remain unchanged after deleting non-existent row."

    
def test_delete_nonexistent_column(df_manager, caplog):
    """
    Attempt to delete a row using a non-existent column 
    and verify that an error is logged.
    """
    df_manager.add([
        {'ID': 1, 'Name': "John", 'Value': 26, 'Timestamp': time.time()}
    ])

    with pytest.raises(ColumnNotFoundError):
        df_manager.delete('NonExistentColumn', 1)


def test_delete_multiple_rows(df_manager):
    """
    Delete multiple rows with the same identifier
    and verify that the DataFrame is updated correctly.
    """
    t = time.time()
    df_manager.add([
        {'ID': 1, 'Name': "John", 'Value': 26, 'Timestamp': t},
        {'ID': 2, 'Name': "Indiana", 'Value': 25, 'Timestamp': t},
        {'ID': 3, 'Name': "Jerry", 'Value': 26, 'Timestamp': t},
        {'ID': 4, 'Name': "Alice", 'Value': 30, 'Timestamp': t}
    ])

    df_manager.delete('Timestamp', t)  
    
    df = df_manager.access_manager()
    assert len(df) == 0, "DataFrame should have no rows after deleting."


def test_update_column_all_rows(df_manager):
    """Update all rows in a column and verify the update."""
    time_val = time.time()
    df_manager.add([
        {'ID': 1, 'Name': "John", 'Value': 10, 'Timestamp': time_val},
        {'ID': 2, 'Name': "Indiana", 'Value': 10, 'Timestamp': time_val},
        {'ID': 3, 'Name': "Jerry", "Value": 10, 'Timestamp': time_val}
    ])
    df_manager.update_column('Value', 10, 9)
    df = df_manager.access_manager()
    assert all(df['Value'] == 9)


def test_update_column_limited_rows(df_manager):
    """Update only a limited number of rows in a column."""
    time_val = time.time()
    df_manager.add([
        {'ID': 1, 'Name': "John", 'Value': 10, 'Timestamp': time_val},
        {'ID': 2, 'Name': "Indiana", 'Value': 10, 'Timestamp': time_val},
        {'ID': 3, 'Name': "Jerry", "Value": 11, 'Timestamp': time_val}
    ])
    df_manager.update_column('Value', 10, 9, 1)
    df = df_manager.access_manager()
    actual_values = df['Value'].tolist()
    expected_values = [9, 10, 11]
    assert (
        actual_values == expected_values
    ), f"Expected {expected_values}, but got {actual_values}"


def test_conditional_update_column(df_manager):
    """Update a column based on a condition in another column."""
    time_val_1 = time.time()
    time_val_2 = time.time()
    df_manager.add([
        {'ID': 1, 'Name': "John", 'Value': 9, 'Timestamp': time_val_1},
        {'ID': 2, 'Name': "Indiana", 'Value': 10, 'Timestamp': time_val_1},
        {'ID': 3, 'Name': "Jerry", 'Value': 11, 'Timestamp': time_val_2},
        {'ID': 4, 'Name': "Alice", 'Value': 12, 'Timestamp': time_val_2}
    ])
    df_manager.conditional_update_column('Timestamp', time_val_1, 'Value', 100)
    df_manager.conditional_update_column('Value', 100, 'Timestamp', time_val_2)
    df = df_manager.access_manager()
    assert (
        all(df['Timestamp'] == time_val_2)
    ), f"Not all 'Timestamp' values were updated to {time_val_2}."


def test_update_rows(df_manager):
    """Replace a row by identifier."""
    time_val = time.time()
    df_manager.add([
        {'ID': 1, 'Name': "John", 'Value': 9, 'Timestamp': time_val},
        {'ID': 2, 'Name': "Indiana", 'Value': 10, 'Timestamp': time_val},
        {'ID': 3, 'Name': "Jerry", 'Value': 11, 'Timestamp': time_val},
        {'ID': 4, 'Name': "Alice", 'Value': 12, 'Timestamp': time_val}
    ])
    updated = df_manager.update_rows(
        'ID', 2, {'ID': 2, 'Name': "Watson", 'Value': 13, 'Timestamp': time_val})
    assert updated['ID'].iloc[0] == 2, "ID was not updated correctly."
    assert updated['Name'].iloc[0] == "Watson", "Name was not updated correctly."
    assert updated['Value'].iloc[0] == 13, "Value was not updated correctly."
    assert updated['Timestamp'].iloc[0] == time_val, "Timestamp was not updated correctly."


def test_filter_and_read_query(df_manager):
    """Test SQL-style filter and read_query."""
    time_val_1 = time.time()
    time.sleep(1)
    time_val_2 = time.time()
    df_manager.add([
        {'ID': 1, 'Name': "John", 'Value': 10, 'Timestamp': time_val_1},
        {'ID': 2, 'Name': "Indiana", 'Value': 10, 'Timestamp': time_val_2},
        {'ID': 3, 'Name': "Jerry", 'Value': 11, 'Timestamp': time_val_1},
        {'ID': 4, 'Name': "Alice", 'Value': 12, 'Timestamp': time_val_2}
    ])
    filtered = df_manager.filter(column="Value", operator=">", value=11, cols_list="ID,Name")
    assert len(filtered) == 1, "Filtered DataFrame should have exactly 1 row."
    assert filtered['ID'].iloc[0] == 4, "The ID from the filtered output is incorrect"
    assert filtered['Name'].iloc[0] == "Alice", "The Name from the filtered output is incorrect"

    query = df_manager.read_query("SELECT Name FROM df WHERE ID = 2")
    assert query['Name'].iloc[0] == "Indiana"


def test_clear(df_manager):
    """
    Verify that the clear method empties the DataFrame.
    """
    df_manager.add([
        {'ID': 1, 'Name': "John", 'Value': 26, 'Timestamp': time.time()}
    ])
    df_manager.clear()
    
    df = df_manager.access_manager()
    assert df.empty, "DataFrame is not empty after clear."


def test_clear_preserves_columns(df_manager):
    """
    After clear, DataFrame should have the same columns as initialized.
    """
    df_manager.add({'ID': 1, 'Name': "A", 'Value': 5, 'Timestamp': 1})
    df_manager.clear()
    df = df_manager.access_manager()
    
    assert (
        set(df.columns) == {'ID', 'Name', 'Value', 'Timestamp'}
    ), "Columns should be preserved after clear."

    assert df.empty, "DataFrame should be empty after clear."
