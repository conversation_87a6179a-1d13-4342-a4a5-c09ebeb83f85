import pytest
from enum import Enum
import pandas as pd
import time
from simu_core.task_table import TaskTable

class taskType(Enum):
    A = 0
    B = 1


@pytest.fixture
def task_table():
    '''Create an instance of a TaskTable'''
    task_table = TaskTable('dataframe', ['task_id', 'taskType', 'periods_delayed'], taskType)
    yield task_table
    task_table.clear()


def test_init_task_table(task_table):
    # assert isinstance(task_table, pd.DataFrame)
    df = task_table.get_all()
    assert isinstance(df, pd.DataFrame),(
        f"Expected Task Manager's underlying df to be a pandas DataFrame, got {type(df)}")
    assert df.empty, f"Expected Task Manager's df to be empty, got {len(df)} rows"
    assert task_table.ID == 0, (
        f"Expected the next row's ID == 0 (no rows added), got {task_table.ID} instead")
    assert task_table.interval == 0, (
        f"Expected the next row's interval == 0 (no rows added),"
        f"got {task_table.interval} instead")
    assert task_table.Replication == 0, (
        f"Expected the next row's replication == 0 (no rows added)," 
        f"got {task_table.Replication} instead")

def test_add_tasks(task_table):
    # many tasks, many tasks format
    tasks = [{'task_id': 0, 'taskType': taskType.A, 'periods_delayed': 0}]
    task_table.add_task(tasks, 0)
    tasks = [{'task_id': 1, 'taskType': taskType.B, 'periods_delayed': 0}]
    task_table.add_task(tasks, 1)
    task_table.increment_interval()
    tasks = [
        {'task_id': 2, 'taskType': taskType.A, 'periods_delayed': 1},
        {'task_id': 3, 'taskType': taskType.B, 'periods_delayed': 2}
    ]
    task_table.add_task(tasks, 1)
    df = task_table.get_all()
    # expected Task Table
    # ID  Replication  interval  timestamps  task_id  taskType  periods_delayed
    # 0   0            0         time        0         A         0
    # 1   0            1         time        1         B         0
    # 2   0            2         time        2         A         1
    # 3   0            2         time        3         B         2
    assert len(df) == 4, f"Expected 4 task entries, got {len(df)} rows"
    assert df['ID'].tolist() == [0, 1, 2, 3], f"Expected [0, 1, 2, 3], got {df['ID'].tolist()}"
    assert (
        df['Replication'].tolist() == [0, 0, 0, 0]
    ), f"Expected [0, 0, 0, 0], got {df['Replication'].tolist()}"
    assert df['interval'].tolist() == [0, 1, 2, 2], f"Expected [0, 1, 2, 2], got {df['interval'].tolist()}"
    assert df['task_id'].tolist() == [0, 1, 2, 3], f"Expected [0, 1, 2, 3], got {df['task_id'].tolist()}"
    assert (
        df['taskType'].tolist() 
            == [ taskType.A.value, taskType.B.value, taskType.A.value, taskType.B.value]
    ), (f"Expected [{taskType.A.value}, {taskType.B.value}, {taskType.A.value}, "
        f"{taskType.B.value}], got {df['taskType']}")
    assert df['periods_delayed'].tolist() == [0, 0, 1, 2], (
        f"Expected [0, 0, 1, 2], got {df['periods_delayed'].tolist()}")


def test_clear_task_table(task_table):
    new_task = [{'task_id': 0, 'taskType': taskType.A, 'periods_delayed': 0}]
    task_table.add(new_task)
    task_table.clear()
    df = task_table.get_all()
    assert df.empty, f"The Task Table was not cleared, having {len(df)} rows"