import pytest
import pandas as pd
import time
from datetime import datetime
from datetime import timed<PERSON>ta
from simu_core.table import Table

@pytest.fixture
def table():
    """Create an instance of a Table."""
    table = Table('dataframe', ['value'])
    yield table
    table.data_manager.clear()


def test_init_table(table):
    """Test correct initialization of an empty Table."""
    df = table.get_all()
    assert isinstance(df, pd.DataFrame), f"Expected df to be a pandas DataFrame, got {type(df)}"
    assert df.empty, f"Expected df to be empty, got {len(df)} rows"
    assert table.ID == 0, f"Expected table.ID to be 0, got {table.ID}"
    assert table.interval == 0, f"Expected table.interval to be 0, got {table.interval}"
    assert table.Replication == 0, f"Expected table.Replication to be 0, got {table.Replication}"


def test_get_all(table):
    """Retrieve all rows in a Table."""
    df = table.data_manager.access_manager()
    time_val_1 = datetime.now()
    time.sleep(0.5)
    time_val_2 = datetime.now()
    table.add([{'value': 3}, {'value': 7}])
    df = table.data_manager.access_manager()
    df.at[0, 'created_at'] = time_val_1
    df.at[0, 'updated_at'] = time_val_1
    df.at[1, 'created_at'] = time_val_2
    df.at[1, 'updated_at'] = time_val_2
    # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time1        time1      3
    # 1   0            0         time2        time2      7
    df = table.get_all()
    expected = {
        'ID': [0, 1],
        'Replication': [0, 0],
        'created_at': [time_val_1, time_val_2],
        'updated_at': [time_val_1, time_val_2],
        'interval': [0, 0],
        'value': [3, 7]
    }

    for col, values in expected.items():
        if col in ['created_at', 'updated_at']:
            for actual_time, expected_time in zip(df[col].tolist(), values):
                actual_time = datetime.fromisoformat(actual_time)
                assert abs(actual_time - expected_time) < timedelta(microseconds=1), f"Time value was expected to be {expected_time}, got {actual_time}"
            continue
        assert df[col].tolist() == values, f"The {col} values were expected to be {values}, instead got {df[col].tolist()}"


def test_get_by_id(table):
    """Retrieve rows by ID and verify their values."""
    items = [{'value': 3}, {'value': 7}, {'value': 9}]
    table.add(items)

    df0 = table.get_by_id(0)
    assert len(df0) == 1, f"Expected 1 row with ID 0, got {len(df0)} rows"
    assert df0.iloc[0]['value'] == 3, f"Expected value 3 with ID 0, got {df0.iloc[0]['value']}"
    
    df1 = table.get_by_id(1)
    assert len(df1) == 1, f"Expected 1 row with ID 1, got {len(df1)} rows"
    assert df1.iloc[0]['value'] == 7, f"Expected value 7 with ID 1, got {df1.iloc[0]['value']}"
    
    df2 = table.get_by_id(2)
    assert len(df2) == 1, f"Expected 1 row with ID 2, got {len(df2)} rows"
    assert df2.iloc[0]['value'] == 9, f"Expected value 9 with ID 2, got {df2.iloc[0]['value']}"


def test_get_by_nonexistent_id(table):
    """Retrieve rows by nonexistent ID and verify an empty output."""
    items = [{'value': 3}, {'value': 7}, {'value': 9}]
    table.add(items)

    df4 = table.get_by_id(4)
    assert df4.empty, f"Expected no matches with ID 4, got {len(df4)} rows"


def test_get_by_interval_no_replications(table):
    """Retrieve rows by interval with no Replications."""
    items = [{'value': 3}]
    table.add(items)

    table.increment_interval()

    items = [{'value': 5}]
    table.add(items)
    # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time        time        3
    # 1   0            1         time        time        5

    df = table.get_by_interval(0)
    expected_0 = [[0, 0, 0, 3]]
    actual_0 = df[['ID', 'Replication', 'interval', 'value']].values.tolist()
    assert len(df) == 1, f"Expected 1 row with interval 0, got {len(df)} rows"
    assert expected_0 == actual_0, f"Row retrieved with interval 0 does not match the expected row"

    df = table.get_by_interval(1)
    expected_1 = [[1, 0, 1, 5]]
    actual_1 = df[['ID', 'Replication', 'interval', 'value']].values.tolist()
    assert len(df) == 1, f"Expected 1 row with interval 1, got {len(df)} rows"
    assert expected_1 == actual_1, f"Row retrieved with interval 1 does not match the expected row"


def test_get_by_interval_one_replication(table):
    """Retrieve rows by interval and most recent Replication."""
    items = [{'value': 3}]
    table.add(items)
    table.increment_interval()

    items = [{'value': 5}]
    table.add(items)
    table.increment_replication()
    
    items = [{'value': 7}]
    table.add(items)
    table.increment_interval()

    items = [{'value': 9}, {'value': 11}]
    table.add(items)
    # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time        time        3
    # 1   0            1         time        time        5
    # 2   1            0         time        time        7
    # 3   1            1         time        time        9
    # 4   1            1         time        time        11

    # get_by_interval retrieves rows that match the given interval AND have the most recent Replication

    df = table.get_by_interval(0)
    expected_0 = [[2, 1, 0, 7]]
    actual_0 = df[['ID', 'Replication', 'interval', 'value']].values.tolist()
    assert len(df) == 1, f"Expected 1 row with interval 0, got {len(df)} rows"
    assert expected_0 == actual_0, f"Row retrieved with interval 0 does not match the expected row"

    df = table.get_by_interval(1)
    expected_1 = [[3, 1, 1, 9], [4, 1, 1, 11]]
    actual_1 = df[['ID', 'Replication', 'interval', 'value']].values.tolist()
    assert len(df) == 2, f"Expected 2 rows with interval 1, got {len(df)} rows"
    assert expected_1 == actual_1, f"Rows retrieved with interval 1 does not match the expected rows"


def test_get_by_nonexistent_interval(table):
    items = [{'value': 3}]
    table.add(items)

    table.increment_interval()

    items = [{'value': 5}]
    table.add(items)
    # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time        time        3
    # 1   0            1         time        time        5

    df2 = table.get_by_interval(2)
    assert df2.empty, f"Expected retrieval of rows with interval 2 to be empty, instead got {len(df2)} rows."


def test_get_by_replication(table):
    items = [{'value': 3}]
    table.add(items)
    table.increment_interval()

    items = [{'value': 5}]
    table.add(items)
    table.increment_replication()
    
    items = [{'value': 7}]
    table.add(items)

    # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time        time        3
    # 1   0            1         time        time        5
    # 2   1            0         time        time        7
    
    df0 = table.get_by_replication(0)
    expected0 = [[0, 0, 0, 3], [1, 0, 1, 5]]
    actual0 = df0[['ID', 'Replication', 'interval', 'value']].values.tolist()
    assert len(df0) == 2, f"Expected 2 rows with Replication 0 , instead got {len(df0)} rows."
    assert expected0 == actual0, f"Rows retrieved with Replication 0 does not match the expected rows"

    df1 = table.get_by_replication(1)
    expected1 = [[2, 1, 0, 7]]
    actual1 = df1[['ID', 'Replication', 'interval', 'value']].values.tolist()
    assert expected1 == actual1, f"Rows retrieved with Replication 0 does not match the expected rows"

    # nonexistent replication #
    df2 = table.get_by_replication(2)
    assert df2.empty, f"Expected retrieval of rows with Replication 2 to be empty, instead got {len(df2)} rows"


def test_get_by_column(table):
    """Retrieves and verifies rows that match a column value"""
    items = [{'value': 3}]
    table.add(items)
    table.increment_interval()

    items = [{'value': 5}]
    table.add(items)
    table.increment_replication()
    
    items = [{'value': 7}]
    table.add(items)
    table.increment_interval()

    items = [{'value': 9}]
    table.add(items)
    # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time        time        3
    # 1   0            1         time        time        5
    # 2   1            0         time        time        7
    # 3   1            1         time        time        9

    df1 = table.get_by_column_value('interval', 1)
    actual1 = df1[['ID', 'Replication', 'interval', 'value']].values.tolist()
    expected1 = [
        [1, 0, 1, 5],
        [3, 1, 1, 9],
    ]
    assert len(df1) == 2, f"Expected 2 rows corresponding to interval 1, got {len(df1)} rows"
    assert actual1 == expected1, f"Rows retrieved with interval = 1 did not match the expected rows"


def test_get_by_column_time(table):
    """Retrieves and verifies rows that match a time value"""
    new_row_1 = [{'value': 3}]
    table.add(new_row_1)
    expected_time = datetime.now()
    new_row_2 = [{'value': 5}]
    table.add(new_row_2)

    df = table.data_manager.access_manager()
    df.at[0, "updated_at"] = expected_time
    df.at[1, "created_at"] = expected_time

    # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time1       time2       3
    # 0   0            0         time2       time3       5

    # time direct comparison with time value and DataFrame
    assert df.iloc[0]['updated_at'] == expected_time, f"Expected time comparison with time value and DataFrame column value to be true, got false"
    assert df.iloc[1]['created_at'] == expected_time, f"Expected time comparison with time value and DataFrame column value to be true, got false"

    str_expected_time = expected_time.strftime('%Y-%m-%d %H:%M:%S.%f')
    
    # time direct comparison with time value and SQL column values
    df = table.read_query(f"SELECT * FROM Table WHERE updated_at = '{str_expected_time}'")
    assert len(df) == 1, f"Expected 1 row to be retrieved by a time value, got {len(df)} rows"

    # SQLDF column-by-column comparison
    query_time = table.read_query(f"SELECT updated_at FROM Table LIMIT 1")['updated_at'].iloc[0]
    df = table.read_query(f"SELECT * FROM Table WHERE created_at = '{query_time}'")
    print(f"DATETIME IS {query_time}")
    assert len(df) == 1, f"Expected 1 row to be retrieved with column-by-column time match, got {len(df)} rows"
    assert df.iloc[0]['created_at'] == query_time, (
        f"Expected the column-by-column time values to be equal, but found inequality")

def test_get_by_column_null(table):
    """Retrieves and verifies rows that match a None/NULL value"""
    item = [{}]
    table.add(item)
    # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time        time        NaN
    df = table.get_by_column_value('value', "Null") # Null parsed to upper
    assert len(df) == 1, f"Expected 1 row to be retrieved by value = NULL, got {len(df)} rows"


def test_get_by_column_nonexistent_val(table):
    """Verifies no rows are retrieved when a value is nonexistent."""
    item = [{'value': 3}]
    table.add(item)
    # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time        time        3
    df = table.get_by_column_value('value', 1)
    assert df.empty, f"Expected no rows to be retrived by a value 1, got {len(df)} rows"


def test_add(table):
    """Add values to the table and verify whether or not they have been added correctly."""
    items = [{'value': 3}]
    table.add(items)
    table.increment_interval()

    items = [{'value': 5}]
    table.add(items)
    table.increment_replication()
    
    items = [{'value': 7}]
    table.add(items)
    table.increment_interval()

    items = [{'value': 9}, {}]
    table.add(items)
    
     # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time        time        3
    # 1   0            1         time        time        5
    # 2   1            0         time        time        7
    # 3   1            1         time        time        9
    # 4   1            1         time        time        NaN
    df = table.get_all()
    expected = {
        'ID': [0, 1, 2, 3, 4],
        'Replication': [0, 0, 1, 1, 1],
        'interval': [0, 1, 0, 1, 1],
        'value': [3, 5, 7, 9, None]
    }
    assert len(df) == 5, f"Expected 5 rows, got {len(df)} rows"
    for col, values in expected.items():
        if col == 'value':
            actual_values = df[col].tolist()
            assert values[:4] == actual_values[:4],(
                 f"Rows 0 ~ 3 do not match the expected values for column value")
            assert pd.isna(df['value'].iloc[4]), (
                f"Expected NaN at row 4, got {df['value'].iloc[4]}")
        else:
            assert df[col].tolist() == values, (
                f"The {col} values were expected to be {values}, instead got {df[col].tolist()}")


def test_update_column_values(table):
    '''Updates rows with a prior column value and verifies the correct number of rows have been updated.'''
    items = [{'value': 3}, {'value': 5}, {'value': 3}]
    table.add(items)

    # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time        time        3
    # 1   0            0         time        time        5
    # 2   0            0         time        time        3

    table.update_column_values('value', 3, 5)
    df = table.get_all()
    # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time        time        5
    # 1   0            0         time        time        5
    # 2   0            0         time        time        5
    assert df['value'].tolist() == [5, 5, 5], (
        f"Expected the updated values to be [5, 5, 5], got {df['value'].tolist()}"
    )

    table.update_column_values('value', 5, 1, 2)
    df = table.get_all()
    # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time        time        1
    # 1   0            0         time        time        1
    # 2   0            0         time        time        5
    assert df['value'].tolist() == [1, 1, 5], (
        f"Expected the updated values to be [1, 1, 5], got {df['value'].tolist()}")

    table.update_column_values('value', 0, 5)
    df = table.get_all()
    # Expects no change from previous Table
    assert (
        df['value'].tolist() == [1, 1, 5]
    ), f"Expected the non-updated values to be [1, 1, 5], got {df['value'].tolist()}"

    # Column existence check
    with pytest.raises(KeyError):
        table.update_column_values('nonexistent_column', 1, 2)


def test_read_query(table):
    '''Performs read SQL queries and ensures correct output'''
    items = [{'value': 3}]
    table.add(items)
    table.increment_interval()

    items = [{'value': 5}]
    table.add(items)
    table.increment_replication()
    
    items = [{'value': 3}]
    table.add(items)

    # Expected Table
    # ID  Replication  interval  created_at  updated_at  value
    # 0   0            0         time        time        3
    # 1   0            1         time        time        5
    # 2   1            0         time        time        3

    df = table.read_query("SELECT COUNT(*) as Count FROM Table WHERE " \
    "value = 3 GROUP BY Replication")
    
    expected_count = [1, 1]
    assert df['Count'].tolist() == expected_count, f"The SQL query output"
    "for COUNT was expected to be [1, 1], got {df['Count'].tolist()}"

    df = table.read_query("SELECT SUM(ID) AS Sum FROM Table WHERE interval = 0 AND value = 3") 
    assert (
        df.iloc[0]['Sum'] == 2
    ), f"The SQL query for SUM of ID was expected to be 2, got {df.iloc[0]['Sum']}"


def test_increment_interval(table):
    """Test incrementing the interval."""
    initial_interval = table.interval
    table.increment_interval()
    assert (
        table.interval == initial_interval + 1
    ), f"Expected interval to be {initial_interval + 1}, got {table.interval}"


def test_increment_replication(table):
    """Test incrementing the replication."""
    initial_Replication = table.Replication
    table.increment_replication()
    assert (
        table.Replication == initial_Replication + 1
    ), f"Expected interval to be {initial_Replication + 1}, got {table.Replication}"


def test_increment_replication_resets_interval(table):
    """Increment the replication and verify that it resets the interval."""
    table.increment_interval()
    initial_replication = table.Replication
    table.increment_replication()
    assert (
        table.Replication == initial_replication + 1
    ), f"Expected Replication to be {initial_replication + 1}, got {table.Replication}"
    assert table.interval == 0, f"Expected interval to be reset to 0, got {table.interval}"
