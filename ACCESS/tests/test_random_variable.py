import pytest
import pandas as pd
import time
import logging
from simu_core.random_variable import RandomVariable
logger = logging.getLogger(__name__)

# PARAMETER VALIDITY TESTS

# binomial tests
def test_invalid_n_binomail():
    """
    Verify a ValueError is thrown if n is less than 0
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('binomial', {'n': -1, 'p': 0.2})
def test_negative_p_binomial():
    """
    Verify a ValueError is thrown if p is less than 0
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('binomial', {'n': 2, 'p': -0.1})
def test_greater_than_1_p_binomial():
    """
    Verify a ValueError is thrown if p is greater than 1
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('binomial', {'n': 2, 'p': 1.1})
def test_general_vaild_n_p_binomial():
    """
    Verify no ValueError is thrown if n is greater than 0 and
    p is between 0 and 1
    """
    try:
        rv = RandomVariable('binomial', {'n': 2, 'p': 0.7})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_zero_edge_case_binomail():
    """
    Verify no ValueError is thrown if n is equal to zero
    and p is equal to 0 
    """
    try:
        rv = RandomVariable('binomial', {'n': 0, 'p': 0})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_one_edge_case_binomial():
    """
    Verify no ValueError is thrown if n is equal to zero
    and p is equal to 1
    """
    try:
        rv = RandomVariable('binomial', {'n': 0, 'p': 1})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')

# bernoulli tests
def test_negative_p_bernoulli():
    """
    Verify a ValueError is thrown if p is less than 0
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('bernoulli', {'p': -0.1})
def test_greater_than_1_p_bernoulli():
    """
    Verify a ValueError is thrown if p is greater than 1
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('bernoulli', {'p': 1.1})
def test_general_valid_p_bernoulli():
    """
    Verify no ValueError is thrown if p is between 0 and 1
    """
    try:
        rv = RandomVariable('bernoulli', {'p': 0.2})
        rv2 = RandomVariable('bernoulli', {'p': 0.7})
        num = rv.sample(1)
        num10 = rv.sample(10)
        num2 = rv2.sample(1)
        num210 = rv2.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_zero_edge_case_bernoulli():
    """
    Verify no ValueError is thrown if p is equal to 0
    """
    try:
        rv = RandomVariable('bernoulli', {'p': 0})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_one_edge_case_bernoulli():
    """
    Verify no ValueError is thrown if p is equal to 1
    """
    try:
        rv = RandomVariable('bernoulli', {'p': 1})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unepected ValueError')

# exponential tests
def test_negative_lambda_exponential():
    """
    Verify a ValueError is thrown if lambda is less than 0
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('exponential', {'lambda': -1})
def test_negative_float_lambda_exponential():
    """
    Verify a ValueError is thrown if lambda is between 0 and -1
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('exponential', {'lambda': -0.1})
def test_zero_edge_case_exponential():
    """
    Verify a ValueError is thrown is lambda is equal to 0
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('exponential', {'lambda': 0})
def test_general_valid_lambda_exponential():
    """
    Verify no Value error is thrown if lambda is greater than 1
    """
    try:
        rv = RandomVariable('exponential', {'lambda': 3})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_float_edge_case_exponential():
    """
    Verify no ValueError is thrown if lambda is a float between 0 and 1
    """
    try:
        rv = RandomVariable('exponential', {'lambda': 0.1})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')

# geometric tests
def test_negative_p_geometric():
    """
    Verify a ValueError is thrown if p is less than or equal to -1
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('geometric', {'p': -1})
def test_negative_float_p_geometric():
    """
    Verify a ValueError is thrown if p is between 0 and -1
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('geometric', {'p': -0.1})
def test_greater_than_1_p_geometric():
    """
    Verify a ValueError is thrown if p is greater than 1
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('geometric', {'p': 1.1})
def test_general_valid_p_geometric():
    """
    Verify no ValueError is thrown if p is between 0 and 1
    """
    try:
        rv = RandomVariable('geometric', {'p': 0.5})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_zero_edge_case_exponential():
    """
    Verify a ValueError is thrown if p is equal to 0
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('geometric', {'p': 0})
def test_one_edge_case_exponential():
    """
    Verify no ValueError is thrown if p is equal to 1
    """
    try:
        rv = RandomVariable('geometric', {'p': 1})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')

# normal tests
def test_std_dev_negative_normal():
    """
    Verify a ValueError is thrown if standard deviation is negative
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('normal', {'mean': 1, 'std_dev': -1})
def test_std_dev_negative_float_normal():
    """
    Verify a ValueError is thrown if standard deviation is between 0 and -1
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('normal', {'mean': 1, 'std_dev': -0.1})
def test_mean_negative_normal():
    """
    Verify no ValueError is thrown if mean is negative
    """
    try:
        rv = RandomVariable('normal', {'mean': -1, 'std_dev': 1})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_zero_edge_case_normal():
    """
    Verify no ValueError is thrown if mean and standard deviation are equal to 0
    """
    try:
        rv = RandomVariable('normal', {'mean': 0, 'std_dev': 0})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_general_valid_normal():
    """
    Verify no ValueError is thrown if mean and standard deviation are greater than 0
    """
    try:
        rv = RandomVariable('normal', {'mean': 1, 'std_dev': 1})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_std_dev_greater_than_1_normal():
    """
    Verify no ValueError is thrown if standard devation is greater than 1
    """
    try:
        rv = RandomVariable('normal', {'mean': 1, 'std_dev': 1.4})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_float_normal():
    """
    Verify no ValueError is thrown if mean and standard deviation are between 0 and 1
    """
    try:
        rv = RandomVariable('normal', {'mean': 0.4, 'std_dev': 0.4})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')

# poisson tests
def test_negative_lambda_poisson():
    """
    Verify a ValueError is thrown if lambda is negative
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('poisson', {'lambda': -4})
def test_negative_float_lambda_poisson():
    """
    Verify a ValueError is thrown if lambda is between 0 and -1
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('poisson', {'lambda': -0.1})
def test_general_valid_lambda_poisson():
    """
    Verify no ValueError is thrown if lambda is greater than 0
    """
    try:
        rv = RandomVariable('poisson', {'lambda': 4.8})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_valid_float_lambda_poisson():
    """
    Verify no ValueError is thrown if lambda is between 0 and 1
    """
    try:
        rv = RandomVariable('poisson', {'lambda': 0.2})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_zero_edge_case_poisson():
    """
    Verify no ValueError is thrown if lambda is equal to 0
    """
    try:
        rv = RandomVariable('poisson', {'lambda': 0})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')

# uniform tests
def test_positive_bounds_uniform():
    """
    Verify no ValueError is thrown for positive bounds
    """
    try:
        rv = RandomVariable('uniform', {'upper_bound': 15, 'lower_bound': 2})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_negative_bounds_uniform():
    """
    Verify no ValueError is thrown for negative bounds
    """
    try:
        rv = RandomVariable('uniform', {'upper_bound': -2, 'lower_bound': -12})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_equal_bounds_uniform():
    """
    Verify no ValueError is thrown for equal bounds
    """
    try:
        rv = RandomVariable('uniform', {'upper_bound': 5, 'lower_bound': 5})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_zero_bounds_uniform():
    """
    Verify no ValueError is thrown for bounds equal to zero
    """
    try:
        rv = RandomVariable('uniform', {'upper_bound': 0, 'lower_bound': 0})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_mixed_signs_bounds_uniform():
    """
    Verify no ValueError is thrown for bounds of mixed signs (one positive bound, one negative bound)
    """
    try:
        rv = RandomVariable('uniform', {'upper_bound': 2, 'lower_bound': -15})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')

# empirical tests
def test_one_outcome_empirical():
    """
    Verify no ValueError is thrown if there is only one outcome with probability 1
    """
    try:
        rv = RandomVariable('empirical', 
                            {'path': 'tests/test_random_variable_input/one_outcome_empirical.txt'})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except ValueError:
        pytest.fail('Unexpected ValueError')
def test_string_outcome_empirical():
    """
    Verify no error is thrown if a string is used as the outcome keys
    """
    try:
        rv = RandomVariable('empirical', 
                        {'path': 'tests/test_random_variable_input/string_outcome_empirical.txt'})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except:
        pytest.fail('Unexpected error')
def test_int_outcome_empirical():
    """
    Verify no error is thrown if an integer is used as the outcome keys
    """
    try:
        rv = RandomVariable('empirical', 
                            {'path': 'tests/test_random_variable_input/int_outcome_empirical.txt'})
        num = rv.sample(1)
        num10 = rv.sample(10)
    except:
        pytest.fail('Unexpected error')
def test_one_probability_less_than_zero_empirical():
    """
    Verify a ValueError is thrown if one outcome has a probability less than 0
    even if all probabilities add to 1
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('empirical', 
            {'path': 'tests/test_random_variable_input/one_probability_less_than_zero_empirical.txt'})
def test_one_probability_greater_than_one_empirical():
    """
    Verify a ValueError is thrown if one outcome has a probability greater than 1
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('empirical', 
            {'path': 'tests/test_random_variable_input/one_probability_greater_than_one_empirical.txt'})
def test_sum_greater_than_1_empirical():
    """
    Verify a ValueError is thrown if the sum of probabilities is greater than 1
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('empirical', 
                    {'path': 'tests/test_random_variable_input/sum_greater_than_1_empirical.txt'})
def test_sum_less_than_1_empirical():
    """
    Verify a ValueError is thrown if the sum of probabilities is less than 1
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('empirical', 
                    {'path': 'tests/test_random_variable_input/sum_less_than_1_empirical.txt'})

def test_file_not_found_empirical():
    """
    Verify a FileNotFoundError is thrown for empirical distribution files that
    do not exist
    """
    with pytest.raises(FileNotFoundError):
        rv = RandomVariable('empirical', {'path': 'this_file_does_not_exist.txt'})

def test_multiple_input_types_empirical():
    """
    Verify a ValueError is thrown if there are multiple lines specifying
    input_type
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('empirical', 
                    {'path': 'tests/test_random_variable_input/multiple_input_types_empirical.txt'})

def test_no_input_type_empirical():
    """
    Verify a ValueError is thrown if there is no line specifying input_type
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('empirical', 
                    {'path': 'tests/test_random_variable_input/no_input_type_empirical.txt'})


def test_input_type_not_on_first_line_empirical():
    """
    Verify a ValueError is thrown if the line specifying input_type isn't the
    first line
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('empirical', 
            {'path': 'tests/test_random_variable_input/input_type_not_on_first_line_empirical.txt'})


def test_input_type_not_valid_empirical():
    """
    Verify a ValueError is thrown if the input_type specfied isn't valid
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('empirical', 
                    {'path': 'tests/test_random_variable_input/input_type_not_valid_empirical.txt'})


# case sensitivity tests
def test_case_sensitivity():
    """
    Verify there is no case sensitivity with distribution names
    """
    try:
        rv = RandomVariable('geometric', {'p': 0.5})
        rv1 = RandomVariable('Geometric', {'p': 0.5})
        rv2 = RandomVariable('BERNOULLI', {'p': 0.5})
        rv3 = RandomVariable('poissoN', {'lambda': 0.5})
        rv4 = RandomVariable('eXpOnEnTiAl', {'lambda': 0.5})
        num1 = rv.sample(1)
        num10 = rv.sample(10)
        num11 = rv1.sample(1)
        num110 = rv1.sample(10)
        num2 = rv2.sample(1)
        num210 = rv2.sample(10)
        num3 = rv3.sample(1)
        num310 = rv3.sample(10)
        num4 = rv4.sample(1)
        num410 = rv4.sample(10)

    except:
        pytest.fail('Unexpected error')

def test_distribution_dne_definition():
    """
    Verify a ValueError is thrown if an invalid distribution is given during 
    initialization
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('this_distribution_dne', {'params': 'some_params'})

def test_distribution_dne_sample():
    """
    Verify a ValueError is thrown if self.distribution of the random variable
    is valid during initialization but invalid during sampling
    """
    with pytest.raises(ValueError):
        rv = RandomVariable('geometric', {'p': 0.5})
        rv.distribution = 'this_distribution_dne'
        num1 = rv.sample(1)