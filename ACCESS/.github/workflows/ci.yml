name: Python Code Style Check

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main

jobs:
  style-check:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.8'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pycodestyle

      - name: Run pycodestyle
        run: |
          pycodestyle . --max-line-length=300
