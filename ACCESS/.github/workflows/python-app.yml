# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: Python application

on:
  push:
    branches: [ "dev_v1.5" ]
  pull_request:
    branches: [ "dev_v1.5" ]

permissions:
  contents: read

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        test_files:
          - tests/test_custom_table.py
          - tests/test_df_manager.py
          - tests/test_event_table.py
          - tests/test_file_util.py
          - tests/test_metrics_table.py
          - tests/test_random_variable.py
          - tests/test_table.py
          - tests/test_task_table.py

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.10
      uses: actions/setup-python@v3
      with:
        python-version: "3.10"
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pandas matplotlib pandasql pyyaml
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    - name: Run all pytests
      run: |
        PYTHONPATH=. pytest -v ${{ matrix.test_files }}
