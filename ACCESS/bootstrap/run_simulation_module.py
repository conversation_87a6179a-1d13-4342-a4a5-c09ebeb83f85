import time
import logging

logger = logging.getLogger(__name__)


class RunSimulationModule:
    """Runs the simulation model using the SimuCore library."""

    def __init__(self, sim_core):
        self.sim_core = sim_core

    def execute(self):
        logger.debug("Running RunSimulationModule")
        print("Running simulation...")

        self.sim_core.execution_start_time = time.time()

        # Replication loop
        while not self.simulation_stopping_criteria_met():

            # Epoch/IGW loop
            while self.sim_core.condition_true():
                self.sim_core.gen_events()
                self.sim_core.gen_tasks()

                # Get tasks due for current epoch and add to task_queue
                task_for_interval = self.sim_core.task_table.get_by_interval(
                    self.sim_core.task_table.interval)
                # Convert dataframe to list of dictionaries
                task_list = task_for_interval.to_dict(orient="records")
                # Sort tasks based on user-defined comparator
                self.sim_core.task_queue.add_tasks_to_queue(task_list)
                self.sim_core.do_tasks() 

                self.sim_core.task_queue.clear()
                self.sim_core.event_table.increment_interval()
                self.sim_core.task_table.increment_interval()

            # At the end of each epoch, generate replication metrics
            self.sim_core.generate_replication_metrics()

            # Reset tables and clear tasks for the next replication
            self.sim_core.reset()
            self.sim_core.task_table.clear()

            # Increment replication counter
            self.sim_core.event_table.increment_replication()
            self.sim_core.task_table.increment_replication()
            self.sim_core.metrics_table.increment_replication()

    def simulation_stopping_criteria_met(self):
        """Check if the simulation stopping criteria is met.
        
        Stopping criteria can be based on:
            - Number of replications
            - Execution time
            - Custom criteria defined by the user

        Returns:
            bool: True if stopping criteria is met, False otherwise.

        Raises:
            ValueError: If the simulation stopping criteria is not 
                recognized.
        """
        if self.sim_core.simulation_stopping_criteria == "num_replications":
            return (self.sim_core.event_table.Replication
                        >= self.sim_core.simulation_stopping_threshold)

        # Check if execution time in seconds is less than the threshold 
        if self.sim_core.simulation_stopping_criteria == "execution_time":
            return ((time.time() - self.sim_core.execution_start_time)
                        >= self.sim_core.simulation_stopping_threshold)

        if self.sim_core.simulation_stopping_criteria == "custom":
            return self.sim_core.custom_simulation_stopping_criteria_met()

        raise ValueError(
            "Simulation stopping criteria not recognized, "
            "options include {'num_replications', 'execution_time', 'custom'}")
