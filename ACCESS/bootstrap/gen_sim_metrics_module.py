import logging
from simu_core import SimuCore

logger = logging.getLogger(__name__)


class GenSimMetricsModule:
    """Generate simulation metrics after the simulation has completed."""

    def __init__(self, sim_core: SimuCore):
        self.sim_core = sim_core

    def execute(self):
        """Generate simulation metrics."""
        logger.debug("Running GenSimMetricsModule")
        print("Generating simulation metrics...")
        metrics = self.sim_core.calculate_statistics()
        self.sim_core.metrics = metrics
        logger.debug("Printing simulation metrics")
        logger.info("Metrics: %s", metrics)
