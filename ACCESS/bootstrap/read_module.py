import logging
import yaml
from simu_core import SimuCore

logger = logging.getLogger(__name__)


class ReadModule:
    """Read input parameter files provided by the user."""

    def __init__(self, sim_core: SimuCore, gen_params_key, cust_params_key, tool_params_key):
        self.sim_core = sim_core
        self.gen_params_key = gen_params_key
        self.cust_params_key = cust_params_key
        self.tool_params_key = tool_params_key

    def execute(self):
        self.sim_core.read_input(
            self.read_gen_params(), self.read_cust_params(), self.read_tool_params())

        # Ensure that the user has initialized an event and task table
        if (not self.sim_core.event_table
                or not self.sim_core.task_table
                or not self.sim_core.task_queue):
            raise RuntimeError(
                "Must initialize an event table (called event_table), "
                "a task table (called task_table), and a task queue (called task_queue)")

        self.sim_core.validate_input()

    def _read_yaml(self, key):
        with open(self.sim_core.file_util.get_file(key), 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)

    def read_gen_params(self):
        return self._read_yaml(self.gen_params_key)
        
    def read_cust_params(self):
        return self._read_yaml(self.cust_params_key)
        
    def read_tool_params(self):
        return self._read_yaml(self.tool_params_key)