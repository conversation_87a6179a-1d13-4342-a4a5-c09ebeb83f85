import pandas as pd
import numpy as np
import os as os
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.integrate import cumtrapz
from scipy.interpolate import interp1d

# read in data
os.chdir("/Volumes/cheps-research/Simulation/INPA/WIP/WIP emmalin/access_sim_cheps")
five_regular = pd.read_csv("output5reg5spec.txt", sep=",")

six_regular = pd.read_csv("output6reg4spec.txt", sep=",")

seven_regular = pd.read_csv("output7reg3spec.txt", sep=",")

eight_regular = pd.read_csv("output8reg2spec.txt", sep=",")

nine_regular = pd.read_csv("output9reg1spec.txt", sep=",")

ten_regular = pd.read_csv("output10reg0spec.txt", sep=",")


#ax = df1.plot(x='x', y='y', kind='line', label='DataFrame 1')

#df1.plot(x='x', y='y', kind='line', ax=ax, label='DataFrame 1')

dfs = {
    "5reg5spec": five_regular,
    "6reg4spec": six_regular,
    "7reg3spec": seven_regular,
    "8reg2spec": eight_regular,
    "9reg1spec": nine_regular,
    "10reg0spec": ten_regular
}


lead_times = pd.DataFrame(columns=['special', 'regular', 'overall'])

for label, df in dfs.items():
    special = float(df.loc[df["Metric"] == "specialty patient average lead time", "Value"].values[0])
    regular = float(df.loc[df["Metric"] == "regular patient average lead time", "Value"].values[0])
    overall = float(df.loc[df["Metric"] == "overall average lead time", "Value"].values[0])
    
    # assign the label as index
    lead_times.loc[label] = [special, regular, overall]
    
    
    
lead_times.plot(kind='line', marker='o')  # Optional: marker='o' for visible points
plt.title("Average Lead Times by Block Configuration")
plt.xlabel("Block Configuration")
plt.ylabel("Average Lead Time")
plt.legend(title="Patient Type")
plt.grid(True)
plt.tight_layout()
plt.show()

    


