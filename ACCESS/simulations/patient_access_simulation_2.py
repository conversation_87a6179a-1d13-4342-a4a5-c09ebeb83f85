import matplotlib.pyplot as plt
from simu_core import SimuCore
from simu_core import FileUtil
from simu_core import EventTable
from simu_core import RandomVariable
from simu_core import TaskTable
from simu_core import TaskQueue
from simu_core import ComparatorValues
from simu_core import Table
from simu_core import MetricsTable
from simu_core import CustomTable
import logging
import pandas as pd
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)

pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)


class eventType(Enum):
    STUDENT_ARRIVAL = 0


class taskType(Enum):
    VACCINATE_STUDENT = 0


class PatientAccess2(SimuCore):
    def __init__(self, file_locations_filename):
        super().__init__(eventType, taskType)
        self.file_util = FileUtil(file_locations_filename)

    def read_input(self, gen_params, cust_params, tool_params):
        print("Reading input")
        
        # Read general parameters
        self.replication_stopping_threshold = gen_params['replication_stopping_threshold']
        self.simulation_stopping_criteria = gen_params['simulation_stopping_criteria']
        if (self.simulation_stopping_criteria == "num_replications" 
                or self.simulation_stopping_criteria == "execution_time"):
            self.simulation_stopping_threshold =  gen_params[self.simulation_stopping_criteria]

        # Read custom parameters 
        distribution_type = cust_params['distribution_type']
        mean = cust_params['mean']
        sd = cust_params['std_dev']
        self.pa1_rv = RandomVariable(
            distribution_type, {'mean': mean, 'std_dev': sd}
        )

        # Read tool parameters and initialize tables
        self.event_table = EventTable(
                tool_params['data-manager'],
                tool_params['event-table-col-names'],
                eventType)
        self.task_table = TaskTable(
            tool_params['data-manager'],
            tool_params['task-table-col-names'],
            taskType)
        self.task_queue = TaskQueue(self.sort_task_comparator)
        self.patient_table = CustomTable(
            tool_params['data-manager'],
            ['patient_id', 'arrival_date', 'departure_date', 'release_block_type'])
        self.metrics_table = MetricsTable(
            tool_params['data-manager'],
            ['num_vaccinated'])

        # Initialize custom variables
        self.patient_id = 0
        self.num_vaccinated = []
        logger.debug(f"Number of days clinic is open: {self.replication_stopping_threshold}")

    def validate_input(self):
        if (self.simulation_stopping_criteria == "num_replications" 
                or self.simulation_stopping_criteria == "execution_time"):
            assert (self.simulation_stopping_threshold > 0)
        assert (self.replication_stopping_threshold > 0)

    def condition_true(self):
        return self.event_table.interval < self.replication_stopping_threshold

    def initialize_persistent_variables(self):
        print("initialize persistent variables")
        self.persistent_data["num_vaccines_given"] = 0
        # self.persistent_data["students"] = 
        # pd.DataFrame(columns=['replication', 'interval', 'departure_date'])

    def gen_events(self):
        # Get the number of students
        num_students = int(self.pa1_rv.sample(1)[0])
        incoming_patients = []
        events = []
        for _ in range(num_students):
            incoming_patients.append({
                'patient_id': self.patient_id,
                'arrival_date': self.event_table.interval,
                'departure_date': None})

            events.append({
                "eventType": eventType.STUDENT_ARRIVAL,
                'patient_id': self.patient_id,
                'arrival_date': self.event_table.interval})

            self.patient_id += 1

        self.patient_table.add(incoming_patients)
        self.event_table.add_event(events)
        self.num_vaccinated.append(num_students)

    def gen_tasks(self):
        interval_events = self.event_table.get_by_interval(
            self.event_table.interval).to_dict(orient="records")
        tasks = []
        for event in interval_events:
            if event["eventType"] == eventType.STUDENT_ARRIVAL.value:
                tasks.append({
                    "taskType": taskType.VACCINATE_STUDENT,
                    "event_id": event["ID"],
                    "patient_id": event["patient_id"],
                    "arrival_date": event["arrival_date"]})

        self.task_table.add_task(tasks)

    def do_tasks(self):
        while not self.task_queue.empty():
            curr_task = self.task_queue.get_next_task()
            if (curr_task["taskType"] == taskType.VACCINATE_STUDENT.value):
                self.patient_table.conditional_update_column_values(
                    "patient_id", curr_task["patient_id"],
                    "departure_date", self.event_table.interval)

    def reset(self):
        self.patient_id = 0
        self.patient_table.data_manager.clear()
        self.num_vaccinated.clear()

    def sort_task_comparator(self, a: dict, b: dict) -> int:
        if a['arrival_date'] > b['arrival_date']:
            return ComparatorValues.LOWER_PRIORITY
        elif a['arrival_date'] < b['arrival_date']:
            return ComparatorValues.HIGHER_PRIORITY
        else:
            return ComparatorValues.EQUAL_PRIORITY

    def generate_reports(self, output_key):
        pd.Series(self.metrics).plot(kind='bar')

        plt.xlabel('Day clinic is open')
        plt.ylabel('Average number of vaccines')
        plt.title('Average number of vaccines given for each day clinic is open')
        plt.savefig(self.file_util.get_file(output_key))
        plt.show()

    def generate_replication_metrics(self):
        num_vaccinated_str = ", ".join(str(x) for x in self.num_vaccinated)
        self.metrics_table.add_metric([{"num_vaccinated": num_vaccinated_str}])

    def calculate_statistics(self):
        data = self.metrics_table.get_all()
        num_vacc_col = np.array(
            [[int(x) for x in row.split(', ')]
            for row in data["num_vaccinated"]])
        index_wise_mean = num_vacc_col.mean(axis=0)

        return index_wise_mean
