import matplotlib.pyplot as plt
import logging
import pandas as pd
from enum import Enum
import numpy as np
import math
import csv
from simu_core import SimuCore
from simu_core import FileUtil
from simu_core import EventTable
from simu_core import RandomVariable
from simu_core import TaskTable
from simu_core import TaskQueue
from simu_core import ComparatorValues
from simu_core import Table
from simu_core import MetricsTable
from simu_core import CustomTable

logger = logging.getLogger(__name__)

pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)


class eventType(Enum):
    REGULAR_ARRIVAL = 0
    SPECIALTY_ARRIVAL = 1
    


class taskType(Enum):
    SCHEDULE_PATIENT = 0


class ACCESS_v3(SimuCore):
    def __init__(self, file_locations_filename):
        super().__init__(eventType, taskType)
        self.file_util = FileUtil(file_locations_filename)

    def read_input(self, gen_params, cust_params, tool_params):
        print("Reading input")
        
        self.event_type_to_str = {
            eventType.REGULAR_ARRIVAL.value: "regular",
            eventType.SPECIALTY_ARRIVAL.value: "specialty"
        }
                
        # Read general parameters
        self.replication_stopping_threshold = gen_params['replication_stopping_threshold']
        self.simulation_stopping_criteria = gen_params['simulation_stopping_criteria']
        if (self.simulation_stopping_criteria == "num_replications"):
            self.simulation_stopping_threshold =  gen_params[self.simulation_stopping_criteria]
        self.interval_length = gen_params['interval_length']

        #INTER-ARRIVALS CHANGED
        self.patient_arrival_rvs = {}

        distribution_type = cust_params["distribution_type"]
        
        self.patient_types = [
            entry.split(":")[0] for entry in cust_params["allowable_patient_to_slot_types"]
        ]
        
        for pt_type in self.patient_types:
            lambda_key = f"{pt_type}_lambda"
        
            lambda_per_min = cust_params[lambda_key] / 1440  # convert per day → per minute
        
            self.patient_arrival_rvs[pt_type] = RandomVariable(
                distribution_type, {"lambda": lambda_per_min}
            )
            
        self.allowable_block_types = {}

        
        self.block_release_pairs = dict(item.split(':', 1) for item in cust_params['block_release_pairs'])
        self.block_release_windows = {
            key: int(value) for key, value in 
            (item.split(':', 1) for item in cust_params['block_release_windows'])
        }
        self.block_release_percent = {
            key: float(value) for key, value in 
            (item.split(':', 1) for item in cust_params['block_release_percent'])
        }
       
        # Read tool parameters and initialize tables
        self.event_table = EventTable(
            tool_params['data-manager'],
            tool_params['event-table-col-names'],
            eventType)
        self.task_table = TaskTable(
            tool_params['data-manager'],
            tool_params['task-table-col-names'],
            taskType)
        self.task_queue = TaskQueue(self.sort_task_comparator)
        self.slot_table = CustomTable(
            tool_params['data-manager'],
            tool_params['slot-table-col-names'])
        self.appointment_table = CustomTable(
            tool_params['data-manager'],
            tool_params['appointment-table-col-names'])
        
        # Create a dictionary that maps slot types to release types
        self.slot_to_release_type = {}
        self.unique_block_types = list(self.block_release_pairs.keys()) 
        
        self.slot_type_capacities = {}
        
        for block_type in self.unique_block_types:
            key = f'num_daily_{block_type}_slots'
            self.slot_type_capacities[block_type] = cust_params[key]
            
        self.scheduling_rules = {}

        for rule in cust_params["allowable_patient_to_slot_types"]:
            patient_type, block_types_str = rule.split(":")
            allowed_block_types = block_types_str.split(",")
        
            #create ranking based on order (first -> most preferred)
            block_type_ranking = {
                block: rank for rank, block in enumerate(allowed_block_types)
            } if len(allowed_block_types) > 1 else None
        
            self.scheduling_rules[patient_type] = {
                "allowed_block_types": allowed_block_types,
                "block_type_ranking": block_type_ranking
            }


        self.num_daily_regular_slots = cust_params["num_daily_regular_slots"]
        self.num_daily_specialty_slots = cust_params["num_daily_specialty_slots"]
            
        slot_rows = []
        ## Loop through all replications, intervals, and slots for each type per interval
        for rep in range(self.simulation_stopping_threshold):
            for interval in range(self.replication_stopping_threshold):
                for block_type in self.unique_block_types:
                    num_slots = cust_params[f'num_daily_{block_type}_slots']  # Dynamically get slot count
                    for _ in range(num_slots):
                        slot_rows.append({
                            "Replication": rep,
                            "interval": interval,
                            "block_type": block_type,
                            "release_block_type": self.block_release_pairs[block_type],
                            "release_epoch": max(interval - self.block_release_windows[block_type], 0),
                            "released_block": "",
                            "capacity": 1,
                            "filled": 0
                        })
        #print("Slot rows:", slot_rows)
        ## Add to slot_table
        self.slot_table.add(slot_rows)


        # Initialize custom variables
        self.task_ids = {}
        #self.reg_task_ids = []
        #self.spec_task_ids = []
        self.num_reg_patient_scheduled = 0
        self.num_spec_patient_scheduled = 0
        self.num_waitlisted = [0] * self.replication_stopping_threshold
        
        
        #METRICS CHANGED
        self.block_utilization_dict = {}
        for block_type in self.unique_block_types:
            self.block_utilization_dict[block_type] = [0] * self.replication_stopping_threshold
        self.patient_demand_dict = {}
        for pt in self.patient_types:
            self.patient_demand_dict[pt] = [0] * self.replication_stopping_threshold
            
        

            
        #print("NEW DICT:", self.patient_demand_dict)
        
   
        # patient lead times 
        self.reg_patient_lead_times = [0] * self.simulation_stopping_threshold #one for every replication
        self.spec_patient_lead_times = [0] * self.simulation_stopping_threshold #one for every replication
        self.overall_patient_lead_times = [0] * self.simulation_stopping_threshold #one for every replication
        # cross-scheduled specialty patients in regular slots
        self.num_special_patients_in_regular_slots = [0] * self.replication_stopping_threshold

        # NOTE: modifed by Claire on 2025-7-11 for block release metrics
        # slots tracking for block release
        self.special_slot_ids = []
        self.regular_slot_ids = []
        self.converted_slot_ids = []
        # patient tracking for block release
        self.num_spec_to_spec_before_release = 0
        self.spec_to_reg_after_release = 0
        self.reg_to_converted_after_release = 0


        # NOTE: modifed by Claire on 2025-7-11 for block release metrics
        ## update slot metrics 
        self.regular_slot_ids = self.slot_table.get_by_column_value('block_type', 'regular')['ID'].tolist()
        self.special_slot_ids = self.slot_table.get_by_column_value('block_type', 'specialty')['ID'].tolist()

        logger.debug(f"Number of days clinic is open: {self.replication_stopping_threshold}")
        
        self.scheduling_summary = {
            patient_type: {
                slot_type: {
                    "before_release": [0] * self.replication_stopping_threshold,
                    "after_release": [0] * self.replication_stopping_threshold,
                }
                for slot_type in self.unique_block_types
            }
            for patient_type in self.patient_types
        }
        
        
        base_columns = [
            'overall demand', 'regular patient demand', 'specialty patient demand',
            'overall average lead time', 'regular patient average lead time', 'specialty patient average lead time',
            'overall slot utilization', 'regular slot utilization', 'specialty slot utilization'
        ]
        
        # Dynamically construct scheduling summary column names
        summary_columns = [
            f"{pt} patient to {slot} slots {when.upper().replace('_', ' ')}"
            for pt in self.patient_types
            for slot in self.unique_block_types
            for when in ["before_release", "after_release"]
        ]
        
        # Final column list
        all_columns = base_columns + summary_columns
        
        # Initialize metrics table
        self.metrics_table = MetricsTable(tool_params['data-manager'], all_columns)

    

    def validate_input(self):
        if (self.simulation_stopping_criteria == "num_replications" 
                or self.simulation_stopping_criteria == "execution_time"):
            assert (self.simulation_stopping_threshold > 0)
        assert (self.replication_stopping_threshold > 0)

    def condition_true(self):
        return self.event_table.interval < self.replication_stopping_threshold

    def initialize_persistent_variables(self):
        #print("initialize persistent variables")
        self.persistent_data["num_vaccines_given"] = 0
        # self.persistent_data["students"] = pd.DataFrame(columns=
        # ['replication', 'interval', 'departure_date'])
    
    
    def gen_events(self):
        events = {}
        demand_counts = {}
        
        event_type_map = {
            "regular": eventType.REGULAR_ARRIVAL,
            "specialty": eventType.SPECIALTY_ARRIVAL
        }
    
        #print("PATIENT TYPESSSSS:", self.patient_types)
        for pt_type in self.patient_types:
            # get RandomVariable object for patient type
            rv = self.patient_arrival_rvs[pt_type]
            timer = 0
            count = 0
            arrivals = []
    
            while timer < self.interval_length * 60:
                iat = rv.sample(1)[0]
                timer += iat
                if timer < self.interval_length * 60:
                    event_type = event_type_map[pt_type]
                    arrivals.append({
                        'eventType': event_type,
                        'arrival_time': timer
                    })
                    count += 1
    
            events[pt_type] = arrivals
            demand_counts[pt_type] = count
    
            #sve demand to dictionary
            self.patient_demand_dict[pt_type][self.event_table.interval] = count
    
        # flatten all event lists into one list
        all_events = [e for sublist in events.values() for e in sublist]
    
        #print(f"Generating {len(all_events)} events")
        #for pt_type, count in demand_counts.items():
            #print(f"Generated {count} {pt_type} patients")
    
        self.event_table.add_event(all_events)

    def gen_tasks(self):
        #print("Generating Tasks")
        interval_events = self.event_table.get_by_interval(
            self.event_table.interval).to_dict(orient="records")
        #print("Interval events:", interval_events)
        tasks = []
        for event in interval_events:
            tasks.append({
                "taskType": taskType.SCHEDULE_PATIENT,
                "event_id": event["ID"],
                "details": event["eventType"],
                "arrival_time": event["arrival_time"]})
          
        self.task_table.add_task(tasks)
        
# =============================================================================
#         # retrieve the tasks just added and categorize them
#         new_tasks = self.task_table.get_by_interval(self.event_table.interval).to_dict(orient="records")
#         for task in tasks:
#            event_type = task["details"]
#            if event_type not in self.task_ids:
#                self.task_ids[event_type] = []
#            self.task_ids[event_type].append(task["ID"])
# =============================================================================
                
    # NOTE: Modified by Claire on 07-11-2025             
    def schedule_patient(self, curr_task, slot_table, allowed_block_types, block_type_ranking=None, patient_type=None):
        # find available slots
        available_slots = slot_table[(slot_table["block_type"].isin(allowed_block_types)) &
                                        ((slot_table["capacity"] - slot_table["filled"]) >= 1) &
                                        (slot_table["interval"] > self.event_table.interval)]
        # if there exist available slots
        if not available_slots.empty:
            soonest_interval = available_slots["interval"].min()
            soonest_slots = available_slots[available_slots["interval"] == soonest_interval]
            # if there are multiple slots available, sort by block type ranking if provided
            if block_type_ranking:
                soonest_slots = soonest_slots.assign(
                    block_priority=lambda df: df["block_type"].map(block_type_ranking)
                ).sort_values("block_priority")
            selected_slot_id = soonest_slots.iloc[0]["ID"]
            block_type = soonest_slots.iloc[0]["block_type"]

            # schedule appointment
            #print("Making an appointment for task", curr_task["ID"], "with type", curr_task['details'], "in slot", selected_slot_id, "with block type", block_type)
            self.slot_table.conditional_update_column_values(
                "ID", selected_slot_id, "filled", 1)       
            self.slot_table.conditional_update_column_values(
                "ID", selected_slot_id, "capacity", 0) 
            self.appointment_table.add([{
                "slot_id": selected_slot_id,
                "task_id": curr_task["ID"],
                "status": "scheduled"}])      
            
        
        # if no available slots, waitlist the patient
        else:
            # add task to be scheduled for next interval
            curr_task["taskType"] = taskType.SCHEDULE_PATIENT
            self.task_table.add_task([curr_task], 1)
            self.num_waitlisted[self.event_table.interval] += 1 

            
    def print_interval_tables(self):
        with open(f'outputRep{self.event_table.Replication}Int{self.event_table.interval}.txt', 'w') as f:
            f.write(f"==== Event Table: Interval {self.event_table.interval} ====\n")
            f.write(self.event_table.data_manager._data_frame.to_string(index=False))
            f.write("\n\n")
            
            f.write(f"==== Task Table: Interval {self.event_table.interval} ====\n")
            f.write(self.task_table.data_manager._data_frame.to_string(index=False))
            f.write("\n\n")
            
            f.write(f"==== Slot Table: Interval {self.event_table.interval} ====\n")
            f.write(self.slot_table.data_manager._data_frame.to_string(index=False))
            f.write("\n\n")
            
            f.write(f"==== Appt Table: Interval {self.event_table.interval} ====\n")
            f.write(self.appointment_table.data_manager._data_frame.to_string(index=False))
            f.write("\n\n")


    def release_blocks(self):
        # find releasable slots
        unfilled_slots = self.slot_table.data_manager.filter("filled", "=", 0, cols_list = "*")
        release_types = list(self.block_release_pairs.keys())
        
        # release slot by slot
        for release_type in release_types:
            block_release_slots = list(unfilled_slots[(unfilled_slots["release_epoch"] == self.event_table.interval + 1)&(unfilled_slots["block_type"] == release_type)]["ID"])
            #print(len(block_release_slots)," total slots")
            release_number = math.ceil(self.block_release_percent[release_type] * len(block_release_slots))
            #print(len(block_release_slots)," releasable slots")
            release_count = 0
            release_iterator = 0
            
            while release_count < release_number and release_iterator < len(block_release_slots):              
                slot_id = block_release_slots[release_iterator]
                slot = self.slot_table.data_manager.filter("ID", "=", slot_id, cols_list = "*").reset_index(drop=True)
                if not slot.loc[0, "block_type"] == slot.loc[0,"release_block_type"]: 
                    slot.loc[0, "released_block"] = slot.loc[0, "block_type"] # record the block type that was released in `released_block`
                    slot.loc[0, "block_type"] = slot.loc[0, "release_block_type"] # change the block type to the release block type
                    # update slot table
                    slot = slot.iloc[0].to_dict()
                    self.slot_table.data_manager.update_rows("ID", slot_id, slot) 
                    # update metrics
                    self.converted_slot_ids.append(slot_id)
                    if slot_id in self.regular_slot_ids:
                        self.regular_slot_ids.append(slot_id) # for now, only release special to regular
                        self.special_slot_ids.remove(slot_id)
                    release_count +=1
                release_iterator +=1
            #print(release_count," of ",len(block_release_slots)," ",release_type," slots were released")


    def do_tasks(self):
        #print("current interval:", self.event_table.interval)
    
        # Local slot table for current replication
        slot_table = self.slot_table.get_by_column_value('Replication', self.event_table.Replication)
    
        while not self.task_queue.empty():
            curr_task = self.task_queue.get_next_task()
    
            if curr_task["taskType"] == taskType.SCHEDULE_PATIENT.value:
                event_type = curr_task["details"]  
                patient_type = self.event_type_to_str[event_type]
                rules = self.scheduling_rules[patient_type]
                # Get patient-specific scheduling rule
                rules = self.scheduling_rules[patient_type]
                allowed_block_types = rules["allowed_block_types"]
                block_type_ranking = rules["block_type_ranking"]
    
                self.schedule_patient(
                    curr_task=curr_task,
                    slot_table=slot_table,
                    allowed_block_types=allowed_block_types,
                    block_type_ranking=block_type_ranking,
                    patient_type=patient_type
                )
    
                # update metrics
                appt_row = self.appointment_table.get_by_column_value('task_id', curr_task["ID"])
                if not appt_row.empty:
                    selected_slot_id = appt_row['slot_id'].values[0]
                    block_row = slot_table[slot_table['ID'] == selected_slot_id]
                    
                    released_block = block_row['released_block'].values[0]
                    original_block = block_row['block_type'].values[0]
                    
                    block_type = released_block if released_block != "" else original_block
                    slot_interval = block_row['interval'].values[0]
                    #slot_release_epoch = block_row['release_epoch'].values[0]
                    #event_interval = self.event_table.interval
    
                    # Track utilization
                    self.block_utilization_dict[block_type][slot_interval] += 1
                    
                    timing = "before_release" if released_block == "" else "after_release"
                    patient_type_str = self.event_type_to_str[curr_task["details"]]
                    
                    # Increment the count for the appropriate combination
                    self.scheduling_summary[patient_type_str][block_type][timing][slot_interval] += 1
    
                    # Avoid rebooking the same slot
                    slot_table.drop(block_row.index, inplace=True)
    
        #self.print_interval_tables()
        self.release_blocks()

        
                        
    def reset(self): # called at the end of each replication
        ######## CLEAR AND RESET ########
        # num scheduled patients 
        self.num_reg_patient_scheduled = 0
        self.num_spec_patient_scheduled = 0
        # patient tracking for block release
        self.num_spec_to_spec_before_release = 0
        self.spec_to_reg_after_release = 0
        self.reg_to_converted_after_release = 0
        # patient lead times
        self.reg_patient_lead_times.clear()
        self.spec_patient_lead_times.clear()
        self.overall_patient_lead_times.clear()
        # cross-scheduled specialty patients in regular slots
        self.num_special_patients_in_regular_slots = [0 for _ in range(self.replication_stopping_threshold)]
        # block release tracking
        self.special_slot_ids.clear()   
        self.regular_slot_ids.clear()
        self.converted_slot_ids.clear()
        
        ######## REINITIALIZE ########
        # task ids
        self.task_ids ={}
        self.reg_patient_lead_times = [0] * self.replication_stopping_threshold
        self.spec_patient_lead_times = [0] * self.replication_stopping_threshold
        self.overall_patient_lead_times = [0] * self.replication_stopping_threshold
        # cross-scheduled specialty patients in regular slots
        self.num_special_patients_in_regular_slots = [0] * self.replication_stopping_threshold
        # block release tracking
        self.special_slot_ids = []
        self.regular_slot_ids = []
        self.converted_slot_ids = []
        
        self.block_utilization_dict = {}
        for block_type in self.unique_block_types:
            self.block_utilization_dict[block_type] = [0] * self.replication_stopping_threshold
        self.patient_demand_dict = {}
        for pt in self.patient_types:
            self.patient_demand_dict[pt] = [0] * self.replication_stopping_threshold

    def sort_task_comparator(self, a: dict, b: dict) -> int:
        if a['arrival_time'] < b['arrival_time']:
            return ComparatorValues.LOWER_PRIORITY
        elif a['arrival_time'] > b['arrival_time']:
            return ComparatorValues.HIGHER_PRIORITY
        else:
            return ComparatorValues.EQUAL_PRIORITY


 # NOTE: Modified by Claire on Jul 8
    def generate_reports(self, output_key):
        print("=================METRICS TABLE==================")
        print(self.metrics_table.data_manager._data_frame)

        # GRAPHS
        # Demand: bar charts of demand for overall, regular and specialty patients (one for each, put in one figure)
        plt.figure(figsize=(10, 6), dpi=500)
        plt.subplot(3, 1, 1)
        pd.Series(self.metrics['overall demand']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average number of patient arrivals (overall)")
        plt.title("Average number of patients arriving each day clinic is open (overall)")
        plt.subplot(3, 1, 2)
        pd.Series(self.metrics['regular patient demand']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average number of patient arrivals (regular)")
        plt.title("Average number of regular patients arriving each day clinic is open (regular)")
        plt.subplot(3, 1, 3)
        pd.Series(self.metrics['specialty patient demand']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average number of patient arrivals (specialty)")
        plt.title("Average number of specialty patients arriving each day clinic is open (specialty)")
        plt.tight_layout()
        plt.savefig(self.file_util.get_file(output_key))
        plt.show()

        # Utilization: bar charts of slot utilization for overall, regular and specialty patients (one for each, put in one figure)
        # NOTE: we ignore the 0th day, since patients who arrive on day 0 can be scheduled day 1 at the earliest
        plt.figure(figsize=(10, 6), dpi=500)
        plt.subplot(3, 1, 1)
        pd.Series(self.metrics['overall slot utilization'])[1:].plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average slot utilization (overall)")
        plt.title("Average slot utilization each day clinic is open (overall)")
        plt.subplot(3, 1, 2)
        pd.Series(self.metrics['regular slot utilization'])[1:].plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average slot utilization (regular)")
        plt.title("Average slot utilization each day clinic is open (regular)")
        plt.subplot(3, 1, 3)
        pd.Series(self.metrics['specialty slot utilization'])[1:].plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average slot utilization (specialty)")
        plt.title("Average slot utilization each day clinic is open (specialty)")
        plt.tight_layout()  
        plt.savefig(self.file_util.get_file(output_key))
        plt.show()
        
        # Lead Time: box plots of lead time for overall, regular and specialty patients (one for each, put in one figure)
        # Create figure and subplots
        fig, axs = plt.subplots(3, 1, figsize=(10, 6), sharex=True, sharey=True, dpi=500)
        df = self.metrics_table.data_manager._data_frame
        # Plot each line on the correct subplot
        df.plot(x='Replication', y='overall average lead time', kind='line', ax=axs[0])
        axs[0].set_title("Average lead time for all patient types")
        axs[0].set_ylabel("Overall")
        
        df.plot(x='Replication', y='regular patient average lead time', kind='line', ax=axs[1])
        axs[1].set_title("Average lead time for regular patients")
        axs[1].set_ylabel("Regular")
        
        df.plot(x='Replication', y='specialty patient average lead time', kind='line', ax=axs[2])
        axs[2].set_title("Average lead time for specialty patients")
        axs[2].set_ylabel("Specialty")
        axs[2].set_xlabel("Replication")
        # Clean up layout and save/show
        plt.tight_layout()
        plt.savefig(self.file_util.get_file(output_key))
        plt.show()
        # Pull the DataFrame
        df = self.metrics_table.data_manager._data_frame
        # Reshape to long format for boxplotting
        metrics_long = pd.melt(
            df,
            id_vars=['Replication'], 
            value_vars=[
                'overall average lead time',
                'regular patient average lead time',
                'specialty patient average lead time'
            ],
            var_name='Patient Type',
            value_name='Lead Time'
        )
        # Create boxplot
        plt.figure(figsize=(10, 4), dpi=500)
        ax = plt.subplot(1, 1, 1)
        metrics_long.boxplot(by='Patient Type', column='Lead Time', ax=ax)
        ax.grid(False)
        # Customize labels and layout
        plt.title("Distribution of Average Lead Times by Patient Type")
        plt.suptitle("")  # Remove pandas default title
        plt.xlabel("Patient Type")
        plt.ylabel("Lead Time")
        # Save and show
        plt.tight_layout()
        plt.savefig(self.file_util.get_file(output_key))
        plt.show()
        
    
            
        with open(f'output{self.num_daily_regular_slots}reg{self.num_daily_specialty_slots}spec.txt', "w", newline="") as f:
            writer = csv.writer(f)
            writer.writerow(["Metric", "Value"])  # Header
            for key, value in self.metrics.items():
                writer.writerow([key, value])

        

    # NOTE: Modified by Claire on Jul 8
    def generate_replication_metrics(self):
        
        # Demand 
        # demand is measured as the number of patients that arrived in the interval
        overall_demand = [0] * self.replication_stopping_threshold

        # Sum demand across all patient types in the dictionary
        for event_name, demand_list in self.patient_demand_dict.items():
            overall_demand = [x + y for x, y in zip(overall_demand, demand_list)]
        
        # Dictionary to store comma-separated strings for each event type
        demand_str_dict = {
            "overall demand": ", ".join(str(x) for x in overall_demand)
        }
        
        demand_str_dict.update({
            event_name: ", ".join(str(x) for x in demand_list)
            for event_name, demand_list in self.patient_demand_dict.items()
        })
        
        

        # Slot Utilization
        # slot utilization is measured as the number of slots utlized in the interval divided by the total number of slots available in the interval
        #self.overall_slot_utilized = [x + y for x, y in zip(self.num_reg_slot_utilized, self.num_spec_slot_utilized)]
        
        #METRICS CHANGED
        #print("BLOCK UTILIZATION DICT:", self.block_utilization_dict)
        #print("DEMAND DICT:", demand_str_dict)
        #print("OVERALL DEMAND:", overall_demand)
        
        slot_utilization_percent_dict = {}
        
        total_capacity = sum(
            self.slot_type_capacities.get(bt, 0)
            for bt in self.unique_block_types
        )
        
        #print("SELF SLOT TYPE CAPACITIES:", self.slot_type_capacities)
        
        # zip together all lists of used slots by interval and sum each interval's usage
        self.overall_slot_utilization = [
            sum(x) for x in zip(*[
                self.block_utilization_dict[bt]
                for bt in self.unique_block_types
            ])
        ]
        
        overall_slot_utilization_percent = [
            f"{(x / total_capacity) * 100:.2f}%" for x in self.overall_slot_utilization
        ]
        
        overall_slot_utilization_str = ", ".join(overall_slot_utilization_percent)


        for block_type in self.unique_block_types:
            used_list = self.block_utilization_dict[block_type]
            denom = self.slot_type_capacities.get(block_type, 1)  # fallback to 1 if unknown 
            if denom == 0:
                denom = 1
            slot_utilization_percent_dict[block_type] = [
                f"{(x / denom) * 100:.2f}%" for x in used_list
            ]
            
        slot_utilization_str_dict = {
            block_type: ", ".join(percent_list)
            for block_type, percent_list in slot_utilization_percent_dict.items()
        }
        
        #print("SLOT UTILIZATION PERCENT DICT", slot_utilization_str_dict)

    
        
        lead_time_dict = {
            patient_type_idx: []
            for patient_type_idx in self.task_ids
        }
        
        #print("TASK_IDs:", self.task_ids)
        
        for appointment in self.appointment_table.get_all().to_dict(orient='records'):
            task_id = appointment['task_id']
            slot_id = appointment['slot_id']
        
            for patient_type_idx, id_list in self.task_ids.items():
                if task_id in id_list:
                    arrival_time = self.task_table.get_by_column_value('ID', task_id)['interval'].values[0]
                    appointment_time = self.slot_table.get_by_column_value('ID', slot_id)['interval'].values[0]
                    lead_time = appointment_time - arrival_time
                    lead_time_dict[patient_type_idx].append(lead_time)
                    
                    
        avg_lead_times = {
            patient_type_idx: np.mean(lead_times) if lead_times else 0
            for patient_type_idx, lead_times in lead_time_dict.items()
        }
        
        overall_lead_times = [lt for lst in lead_time_dict.values() for lt in lst]
        avg_overall_lead_time = np.mean(overall_lead_times) if overall_lead_times else 0



        # Cross-scheduled slots
        num_special_patients_in_regular_slots = ", ".join(str(x) for x in self.num_special_patients_in_regular_slots)    


        flat_summary = {}
        for pt in self.patient_types:
            for st in self.unique_block_types:
                for timing in ["before_release", "after_release"]:
                    key = f"{pt} patient to {st} slots {timing.replace('_', ' ').upper()}"
                    flat_summary[key] = ", ".join(str(x) for x in self.scheduling_summary[pt][st][timing])


        # NOTE: modifed by Claire on 2025-7-10 for block release metrics
        # Add to metric table
        self.metrics_table.add_metric([{
            "overall demand": demand_str_dict["overall demand"],
            "regular patient demand": demand_str_dict["regular"],
            "specialty patient demand": demand_str_dict["specialty"],
            "overall average lead time": avg_overall_lead_time, 
            "regular patient average lead time": avg_lead_times[0], 
            "specialty patient average lead time": avg_lead_times[1],
            "overall slot utilization": overall_slot_utilization_str, 
            "regular slot utilization": slot_utilization_str_dict["regular"], 
            "specialty slot utilization": slot_utilization_str_dict["specialty"],
            **flat_summary
            }])
     
        
        #================DEBUG OUTPUT METRICS TABLE ================#
        print("ABOUT TO PRINT METRIC TABLE FOR THIS REPLICATION ")
        # Get the most recent row from the metrics table
        latest_metrics = self.metrics_table.get_all().tail(1)
        
        #print("DEMAND REGULAR:", self.patient_demand_dict["regular"])
        #print("DEMAND SPECIAL:", self.patient_demand_dict["specialty"])

        print("\n" + "="*50)
        print(f"METRICS FOR REPLICATION {latest_metrics['Replication'].values[0]}")
        print("="*50)
        
        print(f"  Overall Demand:                {latest_metrics['overall demand'].values[0]}")
        print(f"  Regular Patient Demand:        {latest_metrics['regular patient demand'].values[0]}")
        print(f"  Specialty Patient Demand:      {latest_metrics['specialty patient demand'].values[0]}\n")
        
        print(f"  Overall Average Lead Time:     {latest_metrics['overall average lead time'].values[0]}")
        print(f"  Regular Patient Avg Lead Time: {latest_metrics['regular patient average lead time'].values[0]}")
        print(f"  Specialty Patient Avg Lead:    {latest_metrics['specialty patient average lead time'].values[0]}\n")
        print(f"  Num Patients Waitlisted        {self.num_waitlisted}")
        
        print(f" Overall Slot Utilization:       {latest_metrics['overall slot utilization'].values[0]}")
        print(f" Regular Slot Utilization:       {latest_metrics['regular slot utilization'].values[0]}")
        print(f" Specialty Slot Utilization:     {latest_metrics['specialty slot utilization'].values[0]}")

        print("="*50 + "\n")
      

    def calculate_statistics(self):
        data = self.metrics_table.get_all()

        # Demand 
        overall_demand_col = np.array(
            [[int(x) for x in row.split(', ')]
            for row in data["overall demand"]])
        print(overall_demand_col)
        
        index_wise_mean_overall_demand_col = overall_demand_col.mean(axis=0)

        reg_patient_demand_col = np.array(
            [[int(x) for x in row.split(', ')]
            for row in data["regular patient demand"]])
        index_wise_mean_reg_patient_demand_col = reg_patient_demand_col.mean(axis=0)
        
        spec_patient_demand_col = np.array(
            [[int(x) for x in row.split(', ')]
            for row in data["specialty patient demand"]])
        index_wise_mean_spec_patient_demand_col = spec_patient_demand_col.mean(axis=0)

        # Utilization 
        overall_slot_utilization_col = np.array(
            [[float(x.strip('%')) for x in row.split(', ')]
            for row in data["overall slot utilization"]])
        index_wise_mean_overall_slot_utilization_col = overall_slot_utilization_col.mean(axis=0)

        reg_slot_utilization_col = np.array(
            [[float(x.strip('%')) for x in row.split(', ')]
            for row in data["regular slot utilization"]])
        index_wise_mean_reg_slot_utilization_col = reg_slot_utilization_col.mean(axis=0)
        
        spec_slot_utilization_col = np.array(
            [[float(x.strip('%')) for x in row.split(', ')]
            for row in data["specialty slot utilization"]])
        index_wise_mean_spec_slot_utilization_col = spec_slot_utilization_col.mean(axis=0)

        # Lead Time 
        avg_overall_lead_col = data["overall average lead time"]
        avg_overall_lead_time = avg_overall_lead_col.mean()
        avg_reg_lead_col = data["regular patient average lead time"]
        avg_reg_lead_time = avg_reg_lead_col.mean()
        avg_spec_lead_col = data["specialty patient average lead time"] 
        # Convert the column to numeric, coercing errors to NaN
        avg_spec_lead_col = pd.to_numeric(data["specialty patient average lead time"], errors='coerce')

        # Calculate the mean, ignoring NaN values
        avg_spec_lead_time = avg_spec_lead_col.mean()

        # Block Release Metrics
        
        # Specialty → Specialty BEFORE release
        num_spec_to_spec_before_release_col = data["specialty patient to specialty slots BEFORE RELEASE"]
        flattened_spec_to_spec_before = num_spec_to_spec_before_release_col.apply(lambda s: [int(x) for x in s.split(', ')])
        all_spec_to_spec_before = [x for sublist in flattened_spec_to_spec_before for x in sublist]
        avg_num_spec_to_spec_before_release = pd.Series(all_spec_to_spec_before).mean()
        
        # Specialty → Regular AFTER release
        num_spec_to_reg_after_release_col = data["specialty patient to regular slots AFTER RELEASE"]  # fixed typo in "sepcialty"
        flattened_spec_to_reg_after = num_spec_to_reg_after_release_col.apply(lambda s: [int(x) for x in s.split(', ')])
        all_spec_to_reg_after = [x for sublist in flattened_spec_to_reg_after for x in sublist]
        avg_num_spec_to_reg_after_release = pd.Series(all_spec_to_reg_after).mean()
        
        # Regular → Regular AFTER release
        num_reg_to_converted_after_release_col = data["regular patient to regular slots AFTER RELEASE"]
        flattened_reg_to_reg_after = num_reg_to_converted_after_release_col.apply(lambda s: [int(x) for x in s.split(', ')])
        all_reg_to_reg_after = [x for sublist in flattened_reg_to_reg_after for x in sublist]
        avg_num_reg_to_converted_after_release = pd.Series(all_reg_to_reg_after).mean()


        # Return the statistics
        return {
            "overall demand": index_wise_mean_overall_demand_col,
            "regular patient demand": index_wise_mean_reg_patient_demand_col,
            "specialty patient demand": index_wise_mean_spec_patient_demand_col,
            "overall average lead time": avg_overall_lead_time, 
            "regular patient average lead time": avg_reg_lead_time, 
            "specialty patient average lead time": avg_spec_lead_time,
            "overall slot utilization": index_wise_mean_overall_slot_utilization_col, 
            "regular slot utilization": index_wise_mean_reg_slot_utilization_col, 
            "specialty slot utilization": index_wise_mean_spec_slot_utilization_col,
            "specialty patient to specialty slots BEFORE RELEASE": avg_num_spec_to_spec_before_release,
            "sepcialty patient to regular slots AFTER RELEASE": avg_num_spec_to_reg_after_release,
            "regular patient to converted slots AFTER RELEASE": avg_num_reg_to_converted_after_release           
        }