import matplotlib.pyplot as plt
import logging
import pandas as pd
from enum import Enum
import numpy as np
from simu_core import SimuCore
from simu_core import FileUtil
from simu_core import EventTable
from simu_core import RandomVariable
from simu_core import TaskTable
from simu_core import TaskQueue
from simu_core import ComparatorValues
from simu_core import Table
from simu_core import MetricsTable
from simu_core import CustomTable

logger = logging.getLogger(__name__)

pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)


class eventType(Enum):
    REGULAR_ARRIVAL = 0
    SPECIAL_ARRIVAL = 1
    


class taskType(Enum):
    SCHEDULE_PATIENT = 0


class ACCESS_v1(SimuCore):
    def __init__(self, file_locations_filename):
        super().__init__(eventType, taskType)
        self.file_util = FileUtil(file_locations_filename)

    def read_input(self, gen_params, cust_params, tool_params):
        print("Reading input")
        
        # Read general parameters
        self.replication_stopping_threshold = gen_params['replication_stopping_threshold']
        self.simulation_stopping_criteria = gen_params['simulation_stopping_criteria']
        if (self.simulation_stopping_criteria == "num_replications"):
            self.simulation_stopping_threshold =  gen_params[self.simulation_stopping_criteria]
        self.interval_length = gen_params['interval_length']


        # Read custom parameters 
        self.daily_regular_slots = cust_params['num_daily_regular_slots']
        self.daily_special_slots = cust_params['num_daily_special_slots']
        distribution_type = cust_params['distribution_type']
        gen_lambda_per_day = cust_params['lambda'] / 1440 # convert lambda from daily arrivals to minutes
        spec_lambda_scaler = cust_params['spec_lambda_scaler']
        spec_lambda_per_day = gen_lambda_per_day * spec_lambda_scaler
        self.reg_pa_rv = RandomVariable(
            distribution_type, {'lambda': gen_lambda_per_day}
        )
        self.spec_pa_rv = RandomVariable(
            distribution_type, {'lambda': spec_lambda_per_day}
        )
       

        # Read tool parameters and initialize tables
        self.event_table = EventTable(
            tool_params['data-manager'],
            tool_params['event-table-col-names'],
            eventType)
        self.task_table = TaskTable(
            tool_params['data-manager'],
            tool_params['task-table-col-names'],
            taskType)
        self.task_queue = TaskQueue(self.sort_task_comparator)
        self.slot_table = CustomTable(
            tool_params['data-manager'],
            tool_params['slot-table-col-names'])
        self.appointment_table = CustomTable(
            tool_params['data-manager'],
            tool_params['appointment-table-col-names'])
        
        slot_rows = []

        # Loop through all replications, intervals, and slots for each type per interval
        for rep in range(self.simulation_stopping_threshold):
            for interval in range(self.replication_stopping_threshold):
                for _ in range(self.daily_special_slots):
                    slot_rows.append({
                        "Replication": rep,
                        "interval": interval,
                        "block_type": "specialty",
                        "capacity": 1,
                        "filled": 0
                    })
                for _ in range(self.daily_regular_slots):
                    slot_rows.append({
                        "Replication": rep,
                        "interval": interval,
                        "block_type": "regular",
                        "capacity": 1,
                        "filled": 0
                    })
        
        #print("Slot rows:", slot_rows)
        # Add to slot_table
        self.slot_table.add(slot_rows)
        
        # Initialize metric table
        self.metrics_table = MetricsTable(
            tool_params['data-manager'],
            ['overall demand', 'regular patient demand', 'specialty patient demand',
             'overall average wait time', 'regular patient average wait time', 'specialty patient average wait time',
             'overall slot utilization', 'regular slot utilization', 'specialty slot utilization', 'cross-scheduled specialty patients in regular slots'])

        # Initialize custom variables
        self.reg_task_ids = []
        self.spec_task_ids = []
        self.num_waitlisted = [0] * self.replication_stopping_threshold
        
        # Initalize metric variables
        self.reg_patient_demand = []
        self.spec_patient_demand = []
        self.overall_demand = []
       
        self.num_reg_slot_utilized = [0] * self.replication_stopping_threshold
        self.num_spec_slot_utilized = [0] * self.replication_stopping_threshold
        self.overall_slot_utilized = [0] * self.replication_stopping_threshold

        self.reg_patient_wait_times = []
        self.spec_patient_wait_times = []
        self.overall_patient_wait_times = []

        # NOTE: newly added
        self.num_special_patients_in_regular_slots = [0] * self.replication_stopping_threshold
        self.num_reg_patient_scheduled = 0
        self.num_spec_patient_scheduled = 0
        

        logger.debug(f"Number of days clinic is open: {self.replication_stopping_threshold}")
    



    def validate_input(self):
        if (self.simulation_stopping_criteria == "num_replications" 
                or self.simulation_stopping_criteria == "execution_time"):
            assert (self.simulation_stopping_threshold > 0)
        assert (self.replication_stopping_threshold > 0)

    def condition_true(self):
        return self.event_table.interval < self.replication_stopping_threshold

    def initialize_persistent_variables(self):
        print("initialize persistent variables")
        self.persistent_data["num_vaccines_given"] = 0
        # self.persistent_data["students"] = pd.DataFrame(columns=
        # ['replication', 'interval', 'departure_date'])
    
    
    def gen_events(self):

        events = []
        # Generate events for regular patient arrivals 
        timer = 0 
        num_reg_patient = 0
        # generate regular patient arrivals till the end of the interval (measured in minutes)
        while timer < self.interval_length*60:
            # generate IAT
            iat = self.reg_pa_rv.sample(1)[0]  
            # increment timer
            timer += iat
            # fill event details if we haven't gone over the length of the interval
            if timer < self.interval_length*60:
                events.append({
                    'eventType': eventType.REGULAR_ARRIVAL,
                    'arrival_time': timer, 
                })

                num_reg_patient += 1

        # Generate events for specialty pateint arrivals 
        # reset timer 
        timer = 0
        num_spec_patient = 0
        # generate special patient arrivals till the end of the interval (measured in minutes)
        while timer < self.interval_length*60:
            iat = self.spec_pa_rv.sample(1)[0]  
            timer += iat
            if timer < self.interval_length*60:
                events.append({
                    'eventType': eventType.SPECIAL_ARRIVAL,
                    'arrival_time': timer, 
                })
                num_spec_patient += 1
                
                
        print(f"Generating {len(events)} events")
        print(f"Generating {num_reg_patient} regular and {num_spec_patient} special patients")
        for i, e in enumerate(events): 
            #print(f"Adding event {i}: {e}")
            self.event_table.add_event([e])
        #self.event_table.add_event(events)
        
        self.reg_patient_demand.append(num_reg_patient)
        self.spec_patient_demand.append(num_spec_patient)


    def gen_tasks(self):
        print("Generating Tasks")
        interval_events = self.event_table.get_by_interval(
            self.event_table.interval).to_dict(orient="records")
        #print("Interval events:", interval_events)
        tasks = []
        for event in interval_events:
            tasks.append({
                "taskType": taskType.SCHEDULE_PATIENT,
                "event_id": event["ID"],
                "details": event["eventType"],
                "arrival_time": event["arrival_time"]})
          
        self.task_table.add_task(tasks)
        
        # retrieve the tasks just added and categorize them
        new_tasks = self.task_table.get_by_interval(self.event_table.interval).to_dict(orient="records")
        for task in new_tasks:
            if task["details"] == eventType.REGULAR_ARRIVAL.value:
                self.reg_task_ids.append(task["ID"])
                self.reg_patient_demand[self.event_table.interval] += 1
            elif task["details"] == eventType.SPECIAL_ARRIVAL.value:
                self.spec_task_ids.append(task["ID"])
                self.spec_patient_demand[self.event_table.interval] += 1

    def schedule_patient(self, curr_task, slot_table, allowed_block_types, block_type_ranking=None, patient_type=None):
        available_slots = slot_table[(slot_table["block_type"].isin(allowed_block_types)) &
                                        ((slot_table["capacity"] - slot_table["filled"]) >= 1) &
                                        (slot_table["interval"] > self.event_table.interval)]



        if not available_slots.empty:
            soonest_interval = available_slots["interval"].min()
            soonest_slots = available_slots[available_slots["interval"] == soonest_interval]
    
            if block_type_ranking:
                soonest_slots = soonest_slots.assign(
                    block_priority=lambda df: df["block_type"].map(block_type_ranking)
                ).sort_values("block_priority")
    
            selected_slot_id = soonest_slots.iloc[0]["ID"]
     
            print("Making a special appointment for task", curr_task["ID"], "in slot", selected_slot_id)
            self.slot_table.conditional_update_column_values(
                "ID", selected_slot_id, "filled", 1)       
            self.slot_table.conditional_update_column_values(
                "ID", selected_slot_id, "capacity", 0) 

            self.appointment_table.add([{
                "slot_id": selected_slot_id,
                "task_id": curr_task["ID"],
                "status": "scheduled"}])      

            # update metrics
            
            block_type = slot_table[slot_table['ID'] == selected_slot_id]["block_type"].iloc[0]
            appt_interval = slot_table[slot_table['ID'] == selected_slot_id]["interval"].iloc[0]
            self.num_spec_patient_scheduled += 1
            
            if block_type == 'regular':
                self.num_reg_slot_utilized[appt_interval] += 1
                if patient_type == "specialty":
                    self.num_special_patients_in_regular_slots[appt_interval] += 1
            elif block_type == 'specialty':
                self.num_spec_slot_utilized[appt_interval] += 1
            
            if patient_type == "regular":
                self.num_reg_patient_scheduled += 1
            elif patient_type == "specialty":
                self.num_spec_patient_scheduled += 1
            

            print(f"Adding {patient_type} patient to {block_type} slot")
            # update local slot table to avoid rebooking
            slot_table.drop(slot_table[slot_table['ID'] == selected_slot_id].index, inplace=True)



        else:
            # add task for next interval/waitlist patient
            curr_task["taskType"] = taskType.SCHEDULE_PATIENT
            self.task_table.add_task([curr_task], 1)
            self.num_waitlisted[self.event_table.interval] += 1  
            
            
    def print_interval_tables(self):
        with open(f'outputRep{self.event_table.Replication}Int{self.event_table.interval}.txt', 'w') as f:
            f.write(f"==== Event Table: Interval {self.event_table.interval} ====\n")
            f.write(self.event_table.data_manager._data_frame.to_string(index=False))
            f.write("\n\n")
            
            f.write(f"==== Task Table: Interval {self.event_table.interval} ====\n")
            f.write(self.task_table.data_manager._data_frame.to_string(index=False))
            f.write("\n\n")
            
            f.write(f"==== Slot Table: Interval {self.event_table.interval} ====\n")
            f.write(self.slot_table.data_manager._data_frame.to_string(index=False))
            f.write("\n\n")
            
            f.write(f"==== Appt Table: Interval {self.event_table.interval} ====\n")
            f.write(self.appointment_table.data_manager._data_frame.to_string(index=False))
            f.write("\n\n")

        

    def do_tasks(self):
        print("current interval:", self.event_table.interval)
        slot_table = self.slot_table.get_by_column_value('Replication', self.event_table.Replication)
        while not self.task_queue.empty():
            #self.slot_table is not subscriptable, so we need to use get_all() FOR NOW
            curr_task = self.task_queue.get_next_task()
            #print("Current task:", curr_task)
            if curr_task["taskType"] == taskType.SCHEDULE_PATIENT.value:  
                if curr_task["details"] == eventType.SPECIAL_ARRIVAL.value:
                    self.schedule_patient(
                        curr_task=curr_task,
                        slot_table=slot_table,
                        allowed_block_types=["specialty", "regular"],
                        block_type_ranking={"specialty": 0, "regular": 1},
                        patient_type="specialty"
                    )
            
                elif curr_task["details"] == eventType.REGULAR_ARRIVAL.value:
                    self.schedule_patient(
                        curr_task=curr_task,
                        slot_table=slot_table,
                        allowed_block_types=["regular"],
                        block_type_ranking=None,
                        patient_type="regular"
                    )
                
        # FOR DEBUGGING        
        self.print_interval_tables()
                        
    # NOTE: Modified by Claire on Jul 8
    def reset(self):
        self.reg_task_ids.clear()
        self.spec_task_ids.clear()

        self.reg_patient_demand.clear()
        self.spec_patient_demand.clear()
        self.overall_demand.clear()

        self.num_reg_slot_utilized = [0 for _ in range(self.replication_stopping_threshold)]
        self.num_spec_slot_utilized = [0 for _ in range(self.replication_stopping_threshold)]
        self.overall_slot_utilized = [0 for _ in range(self.replication_stopping_threshold)]

        self.reg_patient_wait_times.clear()
        self.spec_patient_wait_times.clear()
        self.overall_patient_wait_times.clear()

        self.num_special_patients_in_regular_slots = [0 for _ in range(self.replication_stopping_threshold)]

        self.num_reg_patient_scheduled = 0
        self.num_spec_patient_scheduled = 0
        
        # reinitialize 
        self.reg_task_ids = []
        self.spec_task_ids = []
        # metric variables
        self.reg_patient_demand = []
        self.spec_patient_demand = []
        self.overall_demand = []
        
        self.num_reg_slot_utilized = [0] * self.replication_stopping_threshold
        self.num_spec_slot_utilized = [0] * self.replication_stopping_threshold
        self.overall_slot_utilized = [0] * self.replication_stopping_threshold

        self.reg_patient_wait_times = []
        self.spec_patient_wait_times = []
        self.overall_patient_wait_times = []

        self.num_special_patients_in_regular_slots = [0] * self.replication_stopping_threshold

        


    def sort_task_comparator(self, a: dict, b: dict) -> int:
        if a['arrival_time'] < b['arrival_time']:
            return ComparatorValues.LOWER_PRIORITY
        elif a['arrival_time'] > b['arrival_time']:
            return ComparatorValues.HIGHER_PRIORITY
        else:
            return ComparatorValues.EQUAL_PRIORITY


    def generate_reports(self, output_key):
        print(self.metrics)

        # Demand: bar charts of demand for overall, regular and specialty patients (one for each, put in one figure)
        plt.figure(figsize=(10, 6))
        plt.subplot(3, 1, 1)
        pd.Series(self.metrics['overall demand']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average number of patient arrivals (overall)")
        plt.title("Average number of patients arriving each day clinic is open (overall)")
        plt.subplot(3, 1, 2)
        pd.Series(self.metrics['regular patient demand']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average number of patient arrivals (regular)")
        plt.title("Average number of regular patients arriving each day clinic is open (regular)")
        plt.subplot(3, 1, 3)
        pd.Series(self.metrics['specialty patient demand']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average number of patient arrivals (specialty)")
        plt.title("Average number of specialty patients arriving each day clinic is open (specialty)")
        plt.tight_layout()

        plt.savefig(self.file_util.get_file(output_key))
        plt.show()

        # Utilization: bar charts of slot utilization for overall, regular and specialty patients (one for each, put in one figure)
        plt.figure(figsize=(10, 6))
        plt.subplot(3, 1, 1)
        pd.Series(self.metrics['overall slot utilization']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average slot utilization (overall)")
        plt.title("Average slot utilization each day clinic is open (overall)")
        plt.subplot(3, 1, 2)
        pd.Series(self.metrics['regular slot utilization']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average slot utilization (regular)")
        plt.title("Average slot utilization each day clinic is open (regular)")
        plt.subplot(3, 1, 3)
        pd.Series(self.metrics['specialty slot utilization']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average slot utilization (specialty)")
        plt.title("Average slot utilization each day clinic is open (specialty)")
        plt.tight_layout()  

        plt.savefig(self.file_util.get_file(output_key))
        plt.show()

        # Lead Time: box plots of lead time for overall, regular and specialty patients (one for each, put in one figure)
        plt.figure(figsize=(10, 6))
        plt.subplot(3, 1, 1)
        pd.Series(self.metrics['overall average wait time']).plot(kind='box')
        plt.ylabel("average lead time (overall)")
        plt.title("Average lead time for all patient types")
        plt.subplot(3, 1, 2)
        pd.Series(self.metrics['regular patient average wait time']).plot(kind='box')
        plt.ylabel("average lead time (regular)")
        plt.title("Average lead time for regular patients")
        plt.subplot(3, 1, 3)
        pd.Series(self.metrics['specialty patient average wait time']).plot(kind='box')
        plt.ylabel("average lead time (specialty)") 
        plt.title("Average lead time for specialty patients")

        plt.tight_layout()
        plt.savefig(self.file_util.get_file(output_key))
        plt.show()

        # Cross-scheduled slots: bar chart of the number of specialty patients scheduled in regular slots
        plt.figure(figsize=(10, 6))
        pd.Series(self.metrics['cross-scheduled specialty patients in regular slots']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Number of specialty patients scheduled in regular slots")
        plt.title("Number of specialty patients scheduled in regular slots each day clinic is open")
        plt.tight_layout()
        plt.savefig(self.file_util.get_file(output_key))
        plt.show()



    def generate_replication_metrics(self):
        
        # Demand 
        # demand is measured as the number of patients that arrived in the interval
        self.overall_demand = [x + y for x, y in zip(self.reg_patient_demand, self.spec_patient_demand)]
        num_regular_demand = ", ".join(str(x) for x in self.reg_patient_demand)
        num_special_demand = ", ".join(str(x) for x in self.spec_patient_demand)
        num_overall_demand = ", ".join(str(x) for x in self.overall_demand)

        # Slot Utilization
        # slot utilization is measured as the number of slots utlized in the interval divided by the total number of slots available in the interval
        self.overall_slot_utilized = [x + y for x, y in zip(self.num_reg_slot_utilized, self.num_spec_slot_utilized)]
        # calculate slot utilization percentage
        #print("self.daily_reg_slots:", self.daily_regular_slots)
        #print("self.daily_special_slots:", self.daily_special_slots)
        #print("num_reg_slot_utilized", self.num_reg_slot_utilized)
        #print("num_spec_slot_utilized", self.num_spec_slot_utilized)
        reg_slot_utilization = [f"{(x / self.daily_regular_slots) * 100:.2f}%" for x in self.num_reg_slot_utilized]
        spec_slot_utilization = [f"{(x / self.daily_special_slots) * 100:.2f}%" for x in self.num_spec_slot_utilized]
        overall_slot_utilization = [f"{(x / (self.daily_regular_slots + self.daily_special_slots)) * 100:.2f}%" for x in self.overall_slot_utilized]

        reg_slot_utilization_str = ", ".join(reg_slot_utilization)
        spec_slot_utilization_str = ", ".join(spec_slot_utilization)
        overall_slot_utilization_str = ", ".join(overall_slot_utilization)
        
        # Lead Time
        # lead time is calculated as the difference between appointment time and arrival_time 
        reg_patient_lead_times = []
        spec_patient_lead_times = []

        for appointment in self.appointment_table.get_all().to_dict(orient='records'):
            # Regular Paitent 
            if appointment['task_id'] in self.reg_task_ids:
                arrival_time = self.task_table.get_by_column_value('ID', appointment['task_id'])['interval'].values[0]
                appointment_time = self.slot_table.get_by_column_value('ID', appointment['slot_id'])['interval'].values[0]
                lead_time = appointment_time - arrival_time
                #print("APT TIME", appointment_time, "arrival time:", arrival_time, "LEAD TIME:", lead_time)
                reg_patient_lead_times.append(lead_time)
            # Specialty Patient
            if appointment['task_id'] in self.spec_task_ids:
                arrival_time = self.task_table.get_by_column_value('ID', appointment['task_id'])['interval'].values[0]
                appointment_time = self.slot_table.get_by_column_value('ID', appointment['slot_id'])['interval'].values[0]
                lead_time = appointment_time - arrival_time
                #print("APT TIME", appointment_time, "arrival time:", arrival_time, "LEAD TIME:", lead_time)
                spec_patient_lead_times.append(lead_time)  

        overall_lead_times = reg_patient_lead_times + spec_patient_lead_times
        # calculate average lead times
        avg_overall_lead_time = np.mean(overall_lead_times) if overall_lead_times else 0
        avg_reg_lead_times = np.mean(reg_patient_lead_times) if reg_patient_lead_times else 0
        avg_spec_lead_times = np.mean(spec_patient_lead_times) if spec_patient_lead_times else 0
                
        # Cross-scheduled slots
        # measured using the percentage of specialty patients that were scheduled in regular slots
        # calculated as the number of specialty patients scheduled in regular slots divided by the total number of specialty patients scheduled
        # sum up all items in num_special_patients_in_regular_slots 

        num_special_patients_in_regular_slots = ", ".join(str(x) for x in self.num_special_patients_in_regular_slots)        
                
        # Add to metric table
        self.metrics_table.add_metric([{
            "overall demand": num_overall_demand,
            "regular patient demand": num_regular_demand,
            "specialty patient demand": num_special_demand,
            "overall average wait time": avg_overall_lead_time, 
            "regular patient average wait time": avg_reg_lead_times, 
            "specialty patient average wait time": avg_spec_lead_times,
            "overall slot utilization": overall_slot_utilization_str, 
            "regular slot utilization": reg_slot_utilization_str, 
            "specialty slot utilization": spec_slot_utilization_str,
            "cross-scheduled specialty patients in regular slots": num_special_patients_in_regular_slots
            }])
     
        
        
        ##### DEBUG #####
        
        print("ABOUT TO PRINT METRIC TABLE FOR THIS REPLICATION ")
        # Get the most recent row from the metrics table
        latest_metrics = self.metrics_table.get_all().tail(1)

        print("\n" + "="*50)
        print(f"METRICS FOR REPLICATION {latest_metrics['Replication'].values[0]}")
        print("="*50)
        
        print(f"  Overall Demand:                {latest_metrics['overall demand'].values[0]}")
        print(f"  Regular Patient Demand:        {latest_metrics['regular patient demand'].values[0]}")
        print(f"  Specialty Patient Demand:      {latest_metrics['specialty patient demand'].values[0]}\n")
        
        print(f"  Overall Average Wait Time:     {latest_metrics['overall average wait time'].values[0]}")
        print(f"  Regular Patient Avg Wait Time: {latest_metrics['regular patient average wait time'].values[0]}")
        print(f"  Specialty Patient Avg Wait:    {latest_metrics['specialty patient average wait time'].values[0]}\n")
        print(f"  Num Patients Waitlisted        {self.num_waitlisted}")
        
        print(f" Overall Slot Utilization:       {latest_metrics['overall slot utilization'].values[0]}")
        print(f" Regular Slot Utilization:       {latest_metrics['regular slot utilization'].values[0]}")
        print(f" Specialty Slot Utilization:     {latest_metrics['specialty slot utilization'].values[0]}")
        print("="*50 + "\n")
        ##### DEBUG #####

    def calculate_statistics(self):
        data = self.metrics_table.get_all()

        # Demand 
        overall_demand_col = np.array(
            [[int(x) for x in row.split(', ')]
            for row in data["overall demand"]])
        print(overall_demand_col)
        
        index_wise_mean_overall_demand_col = overall_demand_col.mean(axis=0)

        reg_patient_demand_col = np.array(
            [[int(x) for x in row.split(', ')]
            for row in data["regular patient demand"]])
        index_wise_mean_reg_patient_demand_col = reg_patient_demand_col.mean(axis=0)
        
        spec_patient_demand_col = np.array(
            [[int(x) for x in row.split(', ')]
            for row in data["specialty patient demand"]])
        index_wise_mean_spec_patient_demand_col = spec_patient_demand_col.mean(axis=0)

        # Utilization 
        overall_slot_utilization_col = np.array(
            [[float(x.strip('%')) for x in row.split(', ')]
            for row in data["overall slot utilization"]])
        index_wise_mean_overall_slot_utilization_col = overall_slot_utilization_col.mean(axis=0)

        reg_slot_utilization_col = np.array(
            [[float(x.strip('%')) for x in row.split(', ')]
            for row in data["regular slot utilization"]])
        index_wise_mean_reg_slot_utilization_col = reg_slot_utilization_col.mean(axis=0)
        
        spec_slot_utilization_col = np.array(
            [[float(x.strip('%')) for x in row.split(', ')]
            for row in data["specialty slot utilization"]])
        index_wise_mean_spec_slot_utilization_col = spec_slot_utilization_col.mean(axis=0)

        # Lead Time 
        avg_overall_wait_col = data["overall average wait time"]
        avg_overall_wait_time = avg_overall_wait_col.mean()
        avg_reg_wait_col = data["regular patient average wait time"]
        avg_reg_wait_time = avg_reg_wait_col.mean()
        avg_spec_wait_col = data["specialty patient average wait time"] 
        avg_spec_wait_time = avg_spec_wait_col.mean()

        # Cross-scheduled slots
        num_special_patients_in_regular_slots_col = np.array(
            [[int(x) for x in row.split(', ')]
            for row in data["cross-scheduled specialty patients in regular slots"]])
        index_wise_mean_num_special_patients_in_regular_slots_col = num_special_patients_in_regular_slots_col.mean(axis=0)

        # Return the statistics
        return {
            "overall demand": index_wise_mean_overall_demand_col,
            "regular patient demand": index_wise_mean_reg_patient_demand_col,
            "specialty patient demand": index_wise_mean_spec_patient_demand_col,
            "overall average wait time": avg_overall_wait_time, 
            "regular patient average wait time": avg_reg_wait_time, 
            "specialty patient average wait time": avg_spec_wait_time,
            "overall slot utilization": index_wise_mean_overall_slot_utilization_col, 
            "regular slot utilization": index_wise_mean_reg_slot_utilization_col, 
            "specialty slot utilization": index_wise_mean_spec_slot_utilization_col,
            "cross-scheduled specialty patients in regular slots": index_wise_mean_num_special_patients_in_regular_slots_col           
        }

