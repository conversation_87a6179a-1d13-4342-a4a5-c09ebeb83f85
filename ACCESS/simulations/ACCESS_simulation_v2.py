import matplotlib.pyplot as plt
import logging
import pandas as pd
from enum import Enum
import numpy as np
from simu_core import SimuCore
from simu_core import FileUtil
from simu_core import EventTable
from simu_core import RandomVariable
from simu_core import TaskTable
from simu_core import TaskQueue
from simu_core import ComparatorValues
from simu_core import Table
from simu_core import MetricsTable
from simu_core import CustomTable

logger = logging.getLogger(__name__)

pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)


class eventType(Enum):
    REGULAR_ARRIVAL = 0
    SPECIAL_ARRIVAL = 1
    


class taskType(Enum):
    SCHEDULE_PATIENT = 0


class ACCESS_v2(SimuCore):
    def __init__(self, file_locations_filename):
        super().__init__(eventType, taskType)
        self.file_util = FileUtil(file_locations_filename)

    def read_input(self, gen_params, cust_params, tool_params):
        print("Reading input")
        
        # Read general parameters
        self.replication_stopping_threshold = gen_params['replication_stopping_threshold']
        self.simulation_stopping_criteria = gen_params['simulation_stopping_criteria']
        if (self.simulation_stopping_criteria == "num_replications"):
            self.simulation_stopping_threshold =  gen_params[self.simulation_stopping_criteria]
        self.interval_length = gen_params['interval_length']


        # Read custom parameters 
        self.daily_regular_slots = cust_params['num_daily_regular_slots']
        self.daily_special_slots = cust_params['num_daily_specialty_slots']
        distribution_type = cust_params['distribution_type']
        gen_lambda_per_day = cust_params['lambda'] / 1440 # convert lambda from daily arrivals to minutes
        spec_lambda_scaler = cust_params['spec_lambda_scaler']
        spec_lambda_per_day = gen_lambda_per_day * spec_lambda_scaler
        self.reg_pa_rv = RandomVariable(
            distribution_type, {'lambda': gen_lambda_per_day}
        )
        self.spec_pa_rv = RandomVariable(
            distribution_type, {'lambda': spec_lambda_per_day}
        )
        
        self.block_release_pairs = dict(item.split(':', 1) for item in cust_params['block_release_pairs'])
        self.block_release_windows = {
            key: int(value) for key, value in 
            (item.split(':', 1) for item in cust_params['block_release_windows'])
        }
       

        # Read tool parameters and initialize tables
        self.event_table = EventTable(
            tool_params['data-manager'],
            tool_params['event-table-col-names'],
            eventType)
        self.task_table = TaskTable(
            tool_params['data-manager'],
            tool_params['task-table-col-names'],
            taskType)
        self.task_queue = TaskQueue(self.sort_task_comparator)
        self.slot_table = CustomTable(
            tool_params['data-manager'],
            tool_params['slot-table-col-names'])
        self.appointment_table = CustomTable(
            tool_params['data-manager'],
            tool_params['appointment-table-col-names'])
        
        # Create a dictionary that maps slot types to release types
        self.slot_to_release_type = {}

        self.unique_block_types = list(self.block_release_pairs.keys()) 
        
        self.unique_block_types = list(self.block_release_pairs.keys()) 
        
        slot_rows = []
        ## Loop through all replications, intervals, and slots for each type per interval
        # Loop through all replications, intervals, and slots for each block type per interval
        for rep in range(self.simulation_stopping_threshold):
            for interval in range(self.replication_stopping_threshold):
                for block_type in self.unique_block_types:
                    num_slots = cust_params[f'num_daily_{block_type}_slots']  # Dynamically get slot count
                    for _ in range(num_slots):
                        slot_rows.append({
                            "Replication": rep,
                            "interval": interval,
                            "block_type": block_type,
                            "release_block_type": self.block_release_pairs[block_type],
                            "release_epoch": max(interval - self.block_release_windows[block_type], 0),
                            "released_block": "",
                            "capacity": 1,
                            "filled": 0
                        })

        #print("Slot rows:", slot_rows)
        ## Add to slot_table
        self.slot_table.add(slot_rows)

        
        # NOTE: modifed by Claire on 2025-7-11 for block release metrics
        # Initialize metric table
        self.metrics_table = MetricsTable(
            tool_params['data-manager'],
            ['overall demand', 'regular patient demand', 'specialty patient demand',
             'overall average lead time', 'regular patient average lead time', 'specialty patient average lead time',
             'overall slot utilization', 'regular slot utilization', 'specialty slot utilization',
             'cross-scheduled specialty patients in regular slots',
             'specialty patient to specialty slots BEFORE block release','sepcialty patient to regular slots AFTER block release',
             'regular patient to converted slots AFTER block release'])

        # Initialize custom variables
        self.task_ids = {}
        #self.reg_task_ids = []
        #self.spec_task_ids = []
        self.num_reg_patient_scheduled = 0
        self.num_spec_patient_scheduled = 0
        self.num_waitlisted = [0] * self.replication_stopping_threshold
        
        # Initalize metric variables
        # demand

        # self.reg_patient_demand = []
        # self.spec_patient_demand = []
        # self.overall_demand = []
        # slot utilization
        
              #METRICS CHANGED

        self.block_utilization_dict = {}
        for block_type in self.unique_block_types:
            self.block_utilization_dict[block_type] = [0] * self.replication_stopping_threshold
        self.patient_demand_dict = {}
        for event in eventType:
            self.patient_demand_dict[event.value] = [0] * self.replication_stopping_threshold
            
        print("NEW DICT:", self.patient_demand_dict)

        self.num_reg_slot_utilized = [0] * self.replication_stopping_threshold
        self.num_spec_slot_utilized = [0] * self.replication_stopping_threshold
        self.overall_slot_utilized = [0] * self.replication_stopping_threshold
        # patient lead times 
        self.reg_patient_lead_times = [0] * self.simulation_stopping_threshold #one for every replication
        self.spec_patient_lead_times = [0] * self.simulation_stopping_threshold #one for every replication
        self.overall_patient_lead_times = [0] * self.simulation_stopping_threshold #one for every replication
        # cross-scheduled specialty patients in regular slots
        self.num_special_patients_in_regular_slots = [0] * self.replication_stopping_threshold

        # NOTE: modifed by Claire on 2025-7-11 for block release metrics
        # slots tracking for block release
        self.special_slot_ids = []
        self.regular_slot_ids = []
        self.converted_slot_ids = []
        # patient tracking for block release
        self.num_spec_to_spec_before_release = 0
        self.spec_to_reg_after_release = 0
        self.reg_to_converted_after_release = 0


        # NOTE: modifed by Claire on 2025-7-11 for block release metrics
        ## update slot metrics 
        self.regular_slot_ids = self.slot_table.get_by_column_value('block_type', 'regular')['ID'].tolist()
        self.special_slot_ids = self.slot_table.get_by_column_value('block_type', 'specialty')['ID'].tolist()

        logger.debug(f"Number of days clinic is open: {self.replication_stopping_threshold}")
    

    def validate_input(self):
        if (self.simulation_stopping_criteria == "num_replications" 
                or self.simulation_stopping_criteria == "execution_time"):
            assert (self.simulation_stopping_threshold > 0)
        assert (self.replication_stopping_threshold > 0)

    def condition_true(self):
        return self.event_table.interval < self.replication_stopping_threshold

    def initialize_persistent_variables(self):
        print("initialize persistent variables")
        self.persistent_data["num_vaccines_given"] = 0
        # self.persistent_data["students"] = pd.DataFrame(columns=
        # ['replication', 'interval', 'departure_date'])
    
    
    def gen_events(self):

        events = []
        # Generate events for regular patient arrivals 
        timer = 0 
        num_reg_patient = 0
        # generate regular patient arrivals till the end of the interval (measured in minutes)
        while timer < self.interval_length*60:
            # generate IAT
            iat = self.reg_pa_rv.sample(1)[0]  
            # increment timer
            timer += iat
            # fill event details if we haven't gone over the length of the interval
            if timer < self.interval_length*60:
                events.append({
                    'eventType': eventType.REGULAR_ARRIVAL,
                    'arrival_time': timer, 
                })

                num_reg_patient += 1

        # Generate events for specialty pateint arrivals 
        timer = 0
        num_spec_patient = 0
        # generate special patient arrivals till the end of the interval (measured in minutes)
        while timer < self.interval_length*60:
            iat = self.spec_pa_rv.sample(1)[0]  
            timer += iat
            if timer < self.interval_length*60:
                events.append({
                    'eventType': eventType.SPECIAL_ARRIVAL,
                    'arrival_time': timer, 
                })
                num_spec_patient += 1
                
                
        print(f"Generating {len(events)} events")
        print(f"Generating {num_reg_patient} regular and {num_spec_patient} special patients")
        self.event_table.add_event(events)
        self.patient_demand_dict[eventType.REGULAR_ARRIVAL.value][self.event_table.interval] = num_reg_patient
        self.patient_demand_dict[eventType.SPECIAL_ARRIVAL.value][self.event_table.interval] = num_spec_patient
        #self.reg_patient_demand.append(num_reg_patient)
        #self.spec_patient_demand.append(num_spec_patient)

    def gen_tasks(self):
        print("Generating Tasks")
        interval_events = self.event_table.get_by_interval(
            self.event_table.interval).to_dict(orient="records")
        #print("Interval events:", interval_events)
        tasks = []
        for event in interval_events:
            tasks.append({
                "taskType": taskType.SCHEDULE_PATIENT,
                "event_id": event["ID"],
                "details": event["eventType"],
                "arrival_time": event["arrival_time"]})
          
        self.task_table.add_task(tasks)

        for task in tasks:
            event_type = task["details"]
            if event_type not in self.task_ids:
                self.task_ids[event_type] = []
            self.task_ids[event_type].append(task["ID"])
                
    # NOTE: Modified by Claire on 07-11-2025             
    def schedule_patient(self, curr_task, slot_table, allowed_block_types, block_type_ranking=None, patient_type=None):
        # find available slots
        available_slots = slot_table[(slot_table["block_type"].isin(allowed_block_types)) &
                                        ((slot_table["capacity"] - slot_table["filled"]) >= 1) &
                                        (slot_table["interval"] > self.event_table.interval)]
        # if there exist available slots
        if not available_slots.empty:
            soonest_interval = available_slots["interval"].min()
            soonest_slots = available_slots[available_slots["interval"] == soonest_interval]
            # if there are multiple slots available, sort by block type ranking if provided
            if block_type_ranking:
                soonest_slots = soonest_slots.assign(
                    block_priority=lambda df: df["block_type"].map(block_type_ranking)
                ).sort_values("block_priority")
            selected_slot_id = soonest_slots.iloc[0]["ID"]
            block_type = soonest_slots.iloc[0]["block_type"]

            # schedule appointment
            print("Making an appointment for task", curr_task["ID"], "with type", curr_task['details'], "in slot", selected_slot_id, "with block type", block_type)
            self.slot_table.conditional_update_column_values(
                "ID", selected_slot_id, "filled", 1)       
            self.slot_table.conditional_update_column_values(
                "ID", selected_slot_id, "capacity", 0) 
            self.appointment_table.add([{
                "slot_id": selected_slot_id,
                "task_id": curr_task["ID"],
                "status": "scheduled"}])      
            
        
        # if no available slots, waitlist the patient
        else:
            # add task to be scheduled for next interval
            curr_task["taskType"] = taskType.SCHEDULE_PATIENT
            self.task_table.add_task([curr_task], 1)
            self.num_waitlisted[self.event_table.interval] += 1 

            
    def print_interval_tables(self):
        with open(f'outputRep{self.event_table.Replication}Int{self.event_table.interval}.txt', 'w') as f:
            f.write(f"==== Event Table: Interval {self.event_table.interval} ====\n")
            f.write(self.event_table.data_manager._data_frame.to_string(index=False))
            f.write("\n\n")
            
            f.write(f"==== Task Table: Interval {self.event_table.interval} ====\n")
            f.write(self.task_table.data_manager._data_frame.to_string(index=False))
            f.write("\n\n")
            
            f.write(f"==== Slot Table: Interval {self.event_table.interval} ====\n")
            f.write(self.slot_table.data_manager._data_frame.to_string(index=False))
            f.write("\n\n")
            
            f.write(f"==== Appt Table: Interval {self.event_table.interval} ====\n")
            f.write(self.appointment_table.data_manager._data_frame.to_string(index=False))
            f.write("\n\n")


    # NOTE: modifed by Claire on 2025-7-11 for block release metrics
    def release_blocks(self):
        # find releasable slots
        unfilled_slots = self.slot_table.data_manager.filter("filled", "=", 0, cols_list = "*")

        unfilled_slots = unfilled_slots[unfilled_slots["Replication"] == self.event_table.Replication]
        block_release_slots = list(unfilled_slots[unfilled_slots["release_epoch"] <= self.event_table.interval]["ID"])
        
        # release slot by slot
        for slot_id in block_release_slots:
            slot = self.slot_table.data_manager.filter("ID", "=", slot_id, cols_list = "*").reset_index(drop=True)
            # if block type != release block type
            if not slot.loc[0, "block_type"] == slot.loc[0,"release_block_type"]: 
                slot.loc[0, "released_block"] = slot.loc[0, "block_type"] # record the block type that was released in `released_block`
                slot.loc[0, "block_type"] = slot.loc[0, "release_block_type"] # change the block type to the release block type
                # update slot table
                slot = slot.iloc[0].to_dict()
                self.slot_table.data_manager.update_rows("ID", slot_id, slot) 
                # update metrics
                self.converted_slot_ids.append(slot_id)
                self.regular_slot_ids.append(slot_id) # for now, only release special to regular
                self.special_slot_ids.remove(slot_id)
        
        #========== DEBUG OUTPUT SLOT TABLE ================#
        with open('slot_table_release_test.txt', 'w') as f:
            f.write("==== Slot Table: Rep 1 ====\n")



    def do_tasks(self):
        print("current interval:", self.event_table.interval)
        # Get local slot table for this interval
        slot_table = self.slot_table.get_by_column_value('Replication', self.event_table.Replication)
        while not self.task_queue.empty():
            # NOTE:self.slot_table is not subscriptable, so we need to use get_all() FOR NOW
            # Get the next task from the queue
            curr_task = self.task_queue.get_next_task()
            # Schedule Type Task
            if curr_task["taskType"] == taskType.SCHEDULE_PATIENT.value: 
                # schedule for special patients
                if curr_task["details"] == eventType.SPECIAL_ARRIVAL.value:
                    self.schedule_patient(
                        curr_task=curr_task,
                        slot_table=slot_table,
                        allowed_block_types=["specialty", "regular"],
                        block_type_ranking={"specialty": 0, "regular": 1},
                        patient_type="specialty"
                    )

                    appt_row = self.appointment_table.get_by_column_value('task_id', curr_task["ID"])
                    if not appt_row.empty:
                        selected_slot_id = self.appointment_table.get_by_column_value('task_id', curr_task["ID"])['slot_id'].values[0]
                        block_type = slot_table[slot_table['ID'] == selected_slot_id]['block_type'].values[0]
                        event_interval = self.event_table.interval
                        slot_interval = slot_table[slot_table['ID'] == selected_slot_id]['interval'].values[0]
                        slot_release_epoch = slot_table[slot_table['ID'] == selected_slot_id]['release_epoch'].values[0]

                        # Update metrics for special patients
                        self.num_spec_patient_scheduled += 1

                        if block_type == "specialty":
                            self.num_spec_slot_utilized[slot_interval] += 1
                            # if appointment scheduled before block release 
                            if event_interval < slot_release_epoch: # block release at the beginning of an interval
                                self.num_spec_to_spec_before_release += 1
                        elif block_type == "regular":
                            self.num_reg_slot_utilized[slot_interval] += 1
                            self.num_special_patients_in_regular_slots[slot_interval] += 1
                            # TODO: check if logic is correct 
                            if event_interval >= slot_release_epoch: 
                                self.spec_to_reg_after_release += 1

                        # update local slot table to avoid rebooking
                        slot_table.drop(slot_table[slot_table['ID'] == selected_slot_id].index, inplace=True)

                # schedule for regular patients 
                elif curr_task["details"] == eventType.REGULAR_ARRIVAL.value:
                    self.schedule_patient(
                        curr_task=curr_task,
                        slot_table=slot_table,
                        allowed_block_types=["regular"],
                        block_type_ranking=None,
                        patient_type="regular"
                    )

                    appt_row = self.appointment_table.get_by_column_value('task_id', curr_task["ID"])
                    if not appt_row.empty:
                        selected_slot_id = self.appointment_table.get_by_column_value('task_id', curr_task["ID"])['slot_id'].values[0]
                        event_interval = self.event_table.interval
                        slot_interval = slot_table[slot_table['ID'] == selected_slot_id]['interval'].values[0]
                        slot_release_epoch = slot_table[slot_table['ID'] == selected_slot_id]['release_epoch'].values[0]

                        # Update Metrics for regular patients
                        self.num_reg_patient_scheduled += 1
                        self.num_reg_slot_utilized[slot_interval] += 1
                        # TODO: check if logic is correct 
                        if event_interval >= slot_release_epoch: 
                            self.reg_to_converted_after_release += 1

                        # update local slot table to avoid rebooking
                        slot_table.drop(slot_table[slot_table['ID'] == selected_slot_id].index, inplace=True)
                
        self.print_interval_tables()
        self.release_blocks()
        
                        
    def reset(self): # called at the end of each replication
        ######## CLEAR AND RESET ########
        # num scheduled patients 
        self.num_reg_patient_scheduled = 0
        self.num_spec_patient_scheduled = 0
        # patient tracking for block release
        self.num_spec_to_spec_before_release = 0
        self.spec_to_reg_after_release = 0
        self.reg_to_converted_after_release = 0
        # task ids
        #self.task_ids.clear()
        #self.reg_task_ids.clear()
        #self.spec_task_ids.clear()
        # demand 
        #self.reg_patient_demand.clear()
        #self.spec_patient_demand.clear()
        #self.overall_demand.clear()
        # slot utilization
        #self.num_reg_slot_utilized = [0 for _ in range(self.replication_stopping_threshold)]
        #self.num_spec_slot_utilized = [0 for _ in range(self.replication_stopping_threshold)]
        #self.overall_slot_utilized = [0 for _ in range(self.replication_stopping_threshold)]
        # patient lead times
        self.reg_patient_lead_times.clear()
        self.spec_patient_lead_times.clear()
        self.overall_patient_lead_times.clear()
        # cross-scheduled specialty patients in regular slots
        self.num_special_patients_in_regular_slots = [0 for _ in range(self.replication_stopping_threshold)]
        # block release tracking
        self.special_slot_ids.clear()   
        self.regular_slot_ids.clear()
        self.converted_slot_ids.clear()
        
        ######## REINITIALIZE ########
        # task ids
        self.task_ids ={}
        #self.reg_task_ids = []
        #self.spec_task_ids = []
        # demand
        #self.reg_patient_demand = []
        #self.spec_patient_demand = []
        #self.overall_demand = []
        # slot utilization
        #self.num_reg_slot_utilized = [0] * self.replication_stopping_threshold
        #self.num_spec_slot_utilized = [0] * self.replication_stopping_threshold
        #self.overall_slot_utilized = [0] * self.replication_stopping_threshold
        # patient lead times
        self.reg_patient_lead_times = [0] * self.replication_stopping_threshold
        self.spec_patient_lead_times = [0] * self.replication_stopping_threshold
        self.overall_patient_lead_times = [0] * self.replication_stopping_threshold
        # cross-scheduled specialty patients in regular slots
        self.num_special_patients_in_regular_slots = [0] * self.replication_stopping_threshold
        # block release tracking
        self.special_slot_ids = []
        self.regular_slot_ids = []
        self.converted_slot_ids = []
        
    def sort_task_comparator(self, a: dict, b: dict) -> int:
        if a['arrival_time'] < b['arrival_time']:
            return ComparatorValues.LOWER_PRIORITY
        elif a['arrival_time'] > b['arrival_time']:
            return ComparatorValues.HIGHER_PRIORITY
        else:
            return ComparatorValues.EQUAL_PRIORITY


 # NOTE: Modified by Claire on Jul 8
    def generate_reports(self, output_key):
        print("=================METRICS TABLE==================")
        print(self.metrics_table.data_manager._data_frame)

        # GRAPHS
        # Demand: bar charts of demand for overall, regular and specialty patients (one for each, put in one figure)
        plt.figure(figsize=(10, 6), dpi=500)
        plt.subplot(3, 1, 1)
        pd.Series(self.metrics['overall demand']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average number of patient arrivals (overall)")
        plt.title("Average number of patients arriving each day clinic is open (overall)")
        plt.subplot(3, 1, 2)
        pd.Series(self.metrics['regular patient demand']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average number of patient arrivals (regular)")
        plt.title("Average number of regular patients arriving each day clinic is open (regular)")
        plt.subplot(3, 1, 3)
        pd.Series(self.metrics['specialty patient demand']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average number of patient arrivals (specialty)")
        plt.title("Average number of specialty patients arriving each day clinic is open (specialty)")
        plt.tight_layout()
        plt.savefig(self.file_util.get_file(output_key))
        plt.show()

        # Utilization: bar charts of slot utilization for overall, regular and specialty patients (one for each, put in one figure)
        # NOTE: we ignore the 0th day, since patients who arrive on day 0 can be scheduled day 1 at the earliest
        plt.figure(figsize=(10, 6), dpi=500)
        plt.subplot(3, 1, 1)
        pd.Series(self.metrics['overall slot utilization'])[1:].plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average slot utilization (overall)")
        plt.title("Average slot utilization each day clinic is open (overall)")
        plt.subplot(3, 1, 2)
        pd.Series(self.metrics['regular slot utilization'])[1:].plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average slot utilization (regular)")
        plt.title("Average slot utilization each day clinic is open (regular)")
        plt.subplot(3, 1, 3)
        pd.Series(self.metrics['specialty slot utilization'])[1:].plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Average slot utilization (specialty)")
        plt.title("Average slot utilization each day clinic is open (specialty)")
        plt.tight_layout()  
        plt.savefig(self.file_util.get_file(output_key))
        plt.show()
        
        # Lead Time: box plots of lead time for overall, regular and specialty patients (one for each, put in one figure)
        # Create figure and subplots
        fig, axs = plt.subplots(3, 1, figsize=(10, 6), sharex=True, sharey=True, dpi=500)
        df = self.metrics_table.data_manager._data_frame
        # Plot each line on the correct subplot
        df.plot(x='Replication', y='overall average lead time', kind='line', ax=axs[0])
        axs[0].set_title("Average lead time for all patient types")
        axs[0].set_ylabel("Overall")
        
        df.plot(x='Replication', y='regular patient average lead time', kind='line', ax=axs[1])
        axs[1].set_title("Average lead time for regular patients")
        axs[1].set_ylabel("Regular")
        
        df.plot(x='Replication', y='specialty patient average lead time', kind='line', ax=axs[2])
        axs[2].set_title("Average lead time for specialty patients")
        axs[2].set_ylabel("Specialty")
        axs[2].set_xlabel("Replication")
        # Clean up layout and save/show
        plt.tight_layout()
        plt.savefig(self.file_util.get_file(output_key))
        plt.show()
        # Pull the DataFrame
        df = self.metrics_table.data_manager._data_frame
        # Reshape to long format for boxplotting
        metrics_long = pd.melt(
            df,
            id_vars=['Replication'], 
            value_vars=[
                'overall average lead time',
                'regular patient average lead time',
                'specialty patient average lead time'
            ],
            var_name='Patient Type',
            value_name='Lead Time'
        )
        # Create boxplot
        plt.figure(figsize=(10, 4), dpi=500)
        ax = plt.subplot(1, 1, 1)
        metrics_long.boxplot(by='Patient Type', column='Lead Time', ax=ax)
        ax.grid(False)
        # Customize labels and layout
        plt.title("Distribution of Average Lead Times by Patient Type")
        plt.suptitle("")  # Remove pandas default title
        plt.xlabel("Patient Type")
        plt.ylabel("Lead Time")
        # Save and show
        plt.tight_layout()
        plt.savefig(self.file_util.get_file(output_key))
        plt.show()

        # Cross-scheduled slots: bar chart of the number of specialty patients scheduled in regular slots
        plt.figure(figsize=(10, 6), dpi=500)
        pd.Series(self.metrics['cross-scheduled specialty patients in regular slots']).plot(kind='bar')
        plt.xlabel("Day clinic is open")
        plt.ylabel("Number of specialty patients scheduled in regular slots")
        plt.title("Number of specialty patients scheduled in regular slots each day clinic is open")
        plt.tight_layout()
        plt.savefig(self.file_util.get_file(output_key))
        plt.show()
        

    # NOTE: Modified by Claire on Jul 8
    def generate_replication_metrics(self):
        
        # Demand 
        # demand is measured as the number of patients that arrived in the interval
        overall_demand = [0] * self.replication_stopping_threshold

        # Sum demand across all patient types in the dictionary
        for event_name, demand_list in self.patient_demand_dict.items():
            overall_demand = [x + y for x, y in zip(overall_demand, demand_list)]
        self.overall_demand = [x + y for x, y in zip(self.reg_patient_demand, self.spec_patient_demand)]
        num_regular_demand = ", ".join(str(x) for x in self.reg_patient_demand)
        num_special_demand = ", ".join(str(x) for x in self.spec_patient_demand)
        num_overall_demand = ", ".join(str(x) for x in self.overall_demand)
        
        # Dictionary to store comma-separated strings for each event type
        demand_str_dict = {
            event_name: ", ".join(str(x) for x in demand_list)
            for event_name, demand_list in self.patient_demand_dict.items()
            }
        

        # Slot Utilization
        # slot utilization is measured as the number of slots utlized in the interval divided by the total number of slots available in the interval
        self.overall_slot_utilized = [x + y for x, y in zip(self.num_reg_slot_utilized, self.num_spec_slot_utilized)]
        # calculate slot utilization percentage
        #print("self.daily_reg_slots:", self.daily_regular_slots)
        #print("self.daily_special_slots:", self.daily_special_slots)
        #print("num_reg_slot_utilized", self.num_reg_slot_utilized)
        #print("num_spec_slot_utilized", self.num_spec_slot_utilized)
        
        #METRICS CHANGED
        print("BLOCK UTILIZATION DICT:", self.block_utilization_dict)
        print("DEMAND DICT:", demand_str_dict)
        
        
        reg_slot_utilization = [f"{(x / self.daily_regular_slots) * 100:.2f}%" for x in self.block_utilization_dict["regular"]]
        spec_slot_utilization = [f"{(x / self.daily_special_slots) * 100:.2f}%" for x in self.block_utilization_dict["specialty"]]
        
        #reg_slot_utilization = [f"{(x / self.daily_regular_slots) * 100:.2f}%" for x in self.num_reg_slot_utilized]
        #spec_slot_utilization = [f"{(x / self.daily_special_slots) * 100:.2f}%" for x in self.num_spec_slot_utilized]

        overall_slot_utilization = [f"{(x / (self.daily_regular_slots + self.daily_special_slots)) * 100:.2f}%" for x in self.overall_slot_utilized]

        reg_slot_utilization_str = ", ".join(reg_slot_utilization)
        spec_slot_utilization_str = ", ".join(spec_slot_utilization)
        overall_slot_utilization_str = ", ".join(overall_slot_utilization)
        
        # Lead Time
        # lead time is calculated as the difference between appointment time and arrival_time 
        reg_patient_lead_times = []
        spec_patient_lead_times = []
        
        print("TASK_IDs:", self.task_ids)

        for appointment in self.appointment_table.get_all().to_dict(orient='records'):
            # Regular Paitent 
            if appointment['task_id'] in self.task_ids[0]:
                arrival_time = self.task_table.get_by_column_value('ID', appointment['task_id'])['interval'].values[0]
                appointment_time = self.slot_table.get_by_column_value('ID', appointment['slot_id'])['interval'].values[0]
                lead_time = appointment_time - arrival_time
                #print("APT TIME", appointment_time, "arrival time:", arrival_time, "LEAD TIME:", lead_time)
                reg_patient_lead_times.append(lead_time)
            # Specialty Patient
            if appointment['task_id'] in self.task_ids[1]:
                arrival_time = self.task_table.get_by_column_value('ID', appointment['task_id'])['interval'].values[0]
                appointment_time = self.slot_table.get_by_column_value('ID', appointment['slot_id'])['interval'].values[0]
                lead_time = appointment_time - arrival_time
                #print("APT TIME", appointment_time, "arrival time:", arrival_time, "LEAD TIME:", lead_time)
                spec_patient_lead_times.append(lead_time)  

        overall_lead_times = reg_patient_lead_times + spec_patient_lead_times
        # calculate average lead times
        avg_overall_lead_time = np.mean(overall_lead_times) if overall_lead_times else 0
        avg_reg_lead_times = np.mean(reg_patient_lead_times) if reg_patient_lead_times else 0
        avg_spec_lead_times = np.mean(spec_patient_lead_times) if spec_patient_lead_times else 0

        # Cross-scheduled slots
        num_special_patients_in_regular_slots = ", ".join(str(x) for x in self.num_special_patients_in_regular_slots)    



        # NOTE: modifed by Claire on 2025-7-10 for block release metrics
        # Add to metric table
        self.metrics_table.add_metric([{
            "overall demand": num_overall_demand,
            "regular patient demand": num_regular_demand,
            "specialty patient demand": num_special_demand,
            "overall average lead time": avg_overall_lead_time, 
            "regular patient average lead time": avg_reg_lead_times, 
            "specialty patient average lead time": avg_spec_lead_times,
            "overall slot utilization": overall_slot_utilization_str, 
            "regular slot utilization": reg_slot_utilization_str, 
            "specialty slot utilization": spec_slot_utilization_str,
            "cross-scheduled specialty patients in regular slots": num_special_patients_in_regular_slots,
            "specialty patient to specialty slots BEFORE block release": self.num_spec_to_spec_before_release,
            "sepcialty patient to regular slots AFTER block release": self.spec_to_reg_after_release,
            "regular patient to converted slots AFTER block release": self.reg_to_converted_after_release
            }])
     
        
        #================DEBUG OUTPUT METRICS TABLE ================#
        print("ABOUT TO PRINT METRIC TABLE FOR THIS REPLICATION ")
        # Get the most recent row from the metrics table
        latest_metrics = self.metrics_table.get_all().tail(1)
        
        print("DEMAND REGULAR:", self.patient_demand_dict[0])
        print("DEMAND SPECIAL:", self.patient_demand_dict[1])

        print("\n" + "="*50)
        print(f"METRICS FOR REPLICATION {latest_metrics['Replication'].values[0]}")
        print("="*50)
        
        print(f"  Overall Demand:                {latest_metrics['overall demand'].values[0]}")
        print(f"  Regular Patient Demand:        {latest_metrics['regular patient demand'].values[0]}")
        print(f"  Specialty Patient Demand:      {latest_metrics['specialty patient demand'].values[0]}\n")
        
        print(f"  Overall Average Lead Time:     {latest_metrics['overall average lead time'].values[0]}")
        print(f"  Regular Patient Avg Lead Time: {latest_metrics['regular patient average lead time'].values[0]}")
        print(f"  Specialty Patient Avg Lead:    {latest_metrics['specialty patient average lead time'].values[0]}\n")
        print(f"  Num Patients Waitlisted        {self.num_waitlisted}")
        
        print(f" Overall Slot Utilization:       {latest_metrics['overall slot utilization'].values[0]}")
        print(f" Regular Slot Utilization:       {latest_metrics['regular slot utilization'].values[0]}")
        print(f" Specialty Slot Utilization:     {latest_metrics['specialty slot utilization'].values[0]}")

        print(f" Cross-scheduled Specialty Patients in Regular Slots: {latest_metrics['cross-scheduled specialty patients in regular slots'].values[0]}")
        print(f" Specialty Patient to Specialty Slots BEFORE Block Release: {latest_metrics['specialty patient to specialty slots BEFORE block release'].values[0]}")
        print(f" Specialty Patient to Regular Slots AFTER Block Release: {latest_metrics['sepcialty patient to regular slots AFTER block release'].values[0]}")
        print(f" Regular Patient to Converted Slots AFTER Block Release: {latest_metrics['regular patient to converted slots AFTER block release'].values[0]}")
        print("="*50 + "\n")
      

    def calculate_statistics(self):
        data = self.metrics_table.get_all()

        # Demand 
        overall_demand_col = np.array(
            [[int(x) for x in row.split(', ')]
            for row in data["overall demand"]])
        print(overall_demand_col)
        
        index_wise_mean_overall_demand_col = overall_demand_col.mean(axis=0)

        reg_patient_demand_col = np.array(
            [[int(x) for x in row.split(', ')]
            for row in data["regular patient demand"]])
        index_wise_mean_reg_patient_demand_col = reg_patient_demand_col.mean(axis=0)
        
        spec_patient_demand_col = np.array(
            [[int(x) for x in row.split(', ')]
            for row in data["specialty patient demand"]])
        index_wise_mean_spec_patient_demand_col = spec_patient_demand_col.mean(axis=0)

        # Utilization 
        overall_slot_utilization_col = np.array(
            [[float(x.strip('%')) for x in row.split(', ')]
            for row in data["overall slot utilization"]])
        index_wise_mean_overall_slot_utilization_col = overall_slot_utilization_col.mean(axis=0)

        reg_slot_utilization_col = np.array(
            [[float(x.strip('%')) for x in row.split(', ')]
            for row in data["regular slot utilization"]])
        index_wise_mean_reg_slot_utilization_col = reg_slot_utilization_col.mean(axis=0)
        
        spec_slot_utilization_col = np.array(
            [[float(x.strip('%')) for x in row.split(', ')]
            for row in data["specialty slot utilization"]])
        index_wise_mean_spec_slot_utilization_col = spec_slot_utilization_col.mean(axis=0)

        # Lead Time 
        avg_overall_lead_col = data["overall average lead time"]
        avg_overall_lead_time = avg_overall_lead_col.mean()
        avg_reg_lead_col = data["regular patient average lead time"]
        avg_reg_lead_time = avg_reg_lead_col.mean()
        avg_spec_lead_col = data["specialty patient average lead time"] 
        # Convert the column to numeric, coercing errors to NaN
        avg_spec_lead_col = pd.to_numeric(data["specialty patient average lead time"], errors='coerce')

        # Calculate the mean, ignoring NaN values
        avg_spec_lead_time = avg_spec_lead_col.mean()

        # Cross-scheduled slots
        num_special_patients_in_regular_slots_col = np.array(
            [[int(x) for x in row.split(', ')]
            for row in data["cross-scheduled specialty patients in regular slots"]])
        index_wise_mean_num_special_patients_in_regular_slots_col = num_special_patients_in_regular_slots_col.mean(axis=0)

        # Block Release Metrics
        num_spec_to_spec_before_release_col = data["specialty patient to specialty slots BEFORE block release"]
        avg_num_spec_to_spec_before_release = num_spec_to_spec_before_release_col.mean()
        num_spec_to_reg_after_release_col = data["sepcialty patient to regular slots AFTER block release"]
        avg_num_spec_to_reg_after_release = num_spec_to_reg_after_release_col.mean()
        num_reg_to_converted_after_release_col = data["regular patient to converted slots AFTER block release"]
        avg_num_reg_to_converted_after_release = num_reg_to_converted_after_release_col.mean()


        # Return the statistics
        return {
            "overall demand": index_wise_mean_overall_demand_col,
            "regular patient demand": index_wise_mean_reg_patient_demand_col,
            "specialty patient demand": index_wise_mean_spec_patient_demand_col,
            "overall average lead time": avg_overall_lead_time, 
            "regular patient average lead time": avg_reg_lead_time, 
            "specialty patient average lead time": avg_spec_lead_time,
            "overall slot utilization": index_wise_mean_overall_slot_utilization_col, 
            "regular slot utilization": index_wise_mean_reg_slot_utilization_col, 
            "specialty slot utilization": index_wise_mean_spec_slot_utilization_col,
            "cross-scheduled specialty patients in regular slots": index_wise_mean_num_special_patients_in_regular_slots_col,
            "specialty patient to specialty slots BEFORE block release": avg_num_spec_to_spec_before_release,
            "sepcialty patient to regular slots AFTER block release": avg_num_spec_to_reg_after_release,
            "regular patient to converted slots AFTER block release": avg_num_reg_to_converted_after_release           
        }