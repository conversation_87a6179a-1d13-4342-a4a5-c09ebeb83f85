#!/usr/bin/env python3
import argparse
import cProfile
import pstats
import io
import logging
from simulations import ACCESS_v3
from bootstrap import ReadModule, InitModule, RunSimulationModule, GenSimMetricsModule, ReportModule

def build_modules():
    sim_core = ACCESS_v3("data/file_locations_ACCESS_v3.txt")
    gen_params_file_key = "gen_params_file"
    cust_params_file_key = "cust_params_file"
    tool_params_file_key = "tool_params_file"
    output_file_key = "report_file"

    modules = [
        ReadModule(sim_core, gen_params_file_key, cust_params_file_key, tool_params_file_key),
        InitModule(sim_core),
        RunSimulationModule(sim_core),
        GenSimMetricsModule(sim_core),
        ReportModule(sim_core, output_file_key)
    ]
    return modules

def run_phase(phase: str):
    modules = build_modules()
    phase_map = {
        "read": 0,
        "init": 1,
        "run": 2,
        "metrics": 3,
        "report": 4,
        "all": None,
    }
    idx = phase_map.get(phase)
    if idx is None:
        # Run the full pipeline
        for m in modules:
            m.execute()
    else:
        modules[idx].execute()

def main():
    parser = argparse.ArgumentParser(description="Profile ACCESS_v3 run phases with cProfile.")
    parser.add_argument("--phase", choices=["all","read","init","run","metrics","report"], default="all",
                        help="Which phase to profile (default: all).")
    parser.add_argument("--sort", choices=["cumtime","tottime","calls","ncalls","time","name"], default="cumtime",
                        help="Sort key for stats (default: cumtime).")
    parser.add_argument("--limit", type=int, default=40, help="Number of lines to print from stats (default: 40).")
    parser.add_argument("--outfile", default="access_v3.prof", help="Save raw profile to this file.")
    parser.add_argument("--log-level", default="WARNING", help="Python logging level (default: WARNING).")
    args = parser.parse_args()

    logging.basicConfig(level=getattr(logging, args.log_level.upper(), logging.WARNING))
    pr = cProfile.Profile()
    pr.enable()
    run_phase(args.phase)
    pr.disable()

    pr.dump_stats(args.outfile)
    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats(args.sort)
    ps.print_stats(args.limit)
    print(s.getvalue())
    print(f"\nSaved raw profile to: {args.outfile} (load with snakeviz, gprof2dot, or pstats)\n")

if __name__ == "__main__":
    main()
