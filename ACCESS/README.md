# Simulation Framework: Coin Toss Example

This repository provides a Python-based simulation framework for organizing and running various simulation projects. As an example, this project implements a coin toss simulation using a modular, extensible framework. The framework is designed to be flexible, allowing you to easily plug in new simulation models by extending the provided core functionality.

## Project Structure

```plaintext
simulation_project/
│
├── simu_core/                    # Core library (general-purpose classes and functions)
│   ├── __init__.py               # Exposes SimuCore
│   └── simu_core.py              # Abstract base class for simulations
│
├── bootstrap/                    # Simulation framework modules
│   ├── __init__.py               # Exposes framework modules
│   ├── read_module.py            # ReadModule implementation (handles input reading)
│   ├── init_module.py            # InitModule implementation (initializes simulation)
│   ├── run_simulation_module.py  # RunSimulationModule implementation (runs simulation)
│   ├── gen_sim_metrics_module.py # GenSimMetricsModule implementation (generates statistics)
│   └── report_module.py          # ReportModule implementation (generates reports)
│
├── simulations/                  # Specific simulations (e.g., coin toss)
│   ├── __init__.py               # Exposes CoinTossSimCore
│   └── coin_toss_simulation.py   # Coin toss simulation implementation
│
├── data/                         # Contains input files for simulations
│   └── input/                    # Input directory
│       └── input_file.txt        # Example input file for coin toss simulation
│
└── main_simulation.py            # Main script to run the simulation
```

## Installation

To set up the project and run the simulation, you'll need Python 3.6+ and the following Python packages:

- `pandas` (for data handling)
- `matplotlib` (for generating reports)
- `pandasql`
- `yaml`

### Step 1: Clone the Repository

```bash
git clone https://github.com/your_username/simulation_project.git
cd simulation_project
```

### Step 2: Set up the Virtual Environment (Optional but Recommended)

```bash
python -m venv venv
source venv/bin/activate        # On Linux/macOS
venv\Scripts\activate           # On Windows
```

### Step 3: Install Dependencies

```bash
pip install pandas matplotlib
```

## Usage

### Input File

The input for the coin toss simulation is stored in the `data/input/coin_toss/gen_params.yaml`. You can modify the number of replications and tosses by editing the file:

```yaml
num_replications: 10
num_tosses: 200
```

### Running the Simulation

To run the coin toss simulation, execute the `main_simulation.py` script:

```bash
python main_simulation.py
```

This will:

1. Read the input file.
2. Initialize the simulation infrastructure.
3. Simulate the specified number of replications and coin tosses per replication.
4. Generate and print basic statistics (mean, median, etc.).
5. Produce a pie chart report of the simulation results.

### Output Example

```plaintext
Number of replications: 10
Number of tosses: 200
Initializing simulation infrastructure...
Running simulation...
Generating simulation metrics...
Metrics: {'mean': 0.49, 'median': 0.0, 'std_dev': 0.5, 'range': 1, 'count': 200, 'heads': 98, 'tails': 102}
```

A pie chart of the results will also be displayed.

## Adding New Simulations

The framework is designed to allow the easy addition of new simulations. To add a new simulation, follow these steps:

1. **Extend `SimuCore`**: Create a new class in the `simulations` folder that extends the `SimuCore` abstract class.
2. **Implement Required Methods**: Override the `random_variable_query` and `read_input` methods.
3. **Add Custom Logic**: Add your specific simulation logic to the new class.

Once added, you can use the existing `bootstrap` modules to structure your new simulation.

## Folder Breakdown

### `simu_core/`

Contains the core library, including the `SimuCore` abstract base class. All simulations extend this core class to ensure consistency and reusability.

### `bootstrap/`

Contains reusable modules for reading input, initializing the simulation, running the simulation, generating metrics, and reporting. These modules are designed to work with any simulation that extends `SimuCore`.

### `simulations/`

Houses specific simulation models. The current implementation includes a coin toss simulation.

### `data/input/`

Holds the input files for the simulations. In this project, it contains a file specifying the number of coin tosses.

## License

CHEPS Property (DO NOT CLONE TO YOUR PERSONAL COMPUTER)
